-- EMERGENCY FIX for 500 errors after login
-- Run this in Supabase SQL Editor immediately

-- 1. Fix profiles table RLS policies
DROP POLICY IF EXISTS "Users can read own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;
DROP POLICY IF EXISTS "Allow service role to insert profiles" ON profiles;

-- Create working RLS policies for profiles
CREATE POLICY "Enable read access for authenticated users on own profile"
  ON profiles FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Enable insert for authenticated users on own profile"
  ON profiles FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = id);

CREATE POLICY "Enable update for authenticated users on own profile"
  ON profiles FOR UPDATE
  TO authenticated
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

CREATE POLICY "Allow service role full access to profiles"
  ON profiles FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- 2. Fix products table RLS policies
DROP POLICY IF EXISTS "Products are viewable by everyone" ON products;

CREATE POLICY "Enable read access for all users on products"
  ON products FOR SELECT
  TO authenticated, anon
  USING (true);

-- 3. Fix categories table RLS policies  
DROP POLICY IF EXISTS "Categories are viewable by everyone" ON categories;

CREATE POLICY "Enable read access for all users on categories"
  ON categories FOR SELECT
  TO authenticated, anon
  USING (true);

-- 4. Fix cart_items table RLS policies
DROP POLICY IF EXISTS "Users can manage own cart items" ON cart_items;
DROP POLICY IF EXISTS "Users can read own cart items" ON cart_items;
DROP POLICY IF EXISTS "Users can insert own cart items" ON cart_items;
DROP POLICY IF EXISTS "Users can update own cart items" ON cart_items;
DROP POLICY IF EXISTS "Users can delete own cart items" ON cart_items;

CREATE POLICY "Enable all operations for authenticated users on own cart items"
  ON cart_items FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- 5. Fix orders table RLS policies
DROP POLICY IF EXISTS "Users can manage own orders" ON orders;

CREATE POLICY "Enable all operations for authenticated users on own orders"
  ON orders FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- 6. Fix order_items table RLS policies
DROP POLICY IF EXISTS "Users can read own order items" ON order_items;

CREATE POLICY "Enable read access for authenticated users on own order items"
  ON order_items FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM orders 
      WHERE orders.id = order_items.order_id 
      AND orders.user_id = auth.uid()
    )
  );

-- 7. Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated, anon;
GRANT SELECT ON public.products TO authenticated, anon;
GRANT SELECT ON public.categories TO authenticated, anon;
GRANT ALL ON public.profiles TO authenticated;
GRANT ALL ON public.cart_items TO authenticated;
GRANT ALL ON public.orders TO authenticated;
GRANT SELECT ON public.order_items TO authenticated;

-- 8. Ensure RLS is enabled on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE cart_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;

-- 9. Create the missing profile for the current user
INSERT INTO profiles (id, email, full_name, role, created_at, updated_at)
VALUES (
  '6380b67d-2976-43da-9347-d39d50587d11',
  'gg@g',
  'User',
  'user',
  NOW(),
  NOW()
)
ON CONFLICT (id) DO UPDATE SET
  email = EXCLUDED.email,
  updated_at = NOW();

SELECT 'Emergency RLS fix completed!' as status;
