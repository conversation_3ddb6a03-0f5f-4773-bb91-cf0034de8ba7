-- Fix for foreign key constraint violation
-- The user exists in your app but not in auth.users table

-- 1. First, let's see what users actually exist in auth.users
SELECT 'Current users in auth.users:' as info;
SELECT id, email, created_at FROM auth.users ORDE<PERSON> <PERSON>Y created_at DESC LIMIT 10;

-- 2. Check if the problematic user exists
SELECT 'Checking for problematic user:' as info;
SELECT id, email FROM auth.users WHERE id = 'e1cc8c46-45f8-4cee-958d-5630ef3f1838';

-- 3. Let's see what profiles already exist
SELECT 'Current profiles:' as info;
SELECT id, email, full_name FROM profiles OR<PERSON><PERSON> <PERSON>Y created_at DESC LIMIT 10;

-- 4. TEMPORARILY disable the foreign key constraint to allow profile creation
ALTER TABLE profiles DROP CONSTRAINT IF EXISTS profiles_id_fkey;

-- 5. Now create the profile without the foreign key constraint
INSERT INTO profiles (id, email, full_name, role, created_at, updated_at)
VALUES (
  'e1cc8c46-45f8-4cee-958d-5630ef3f1838',
  'xas<PERSON><PERSON>@gmail.com',
  'gg',
  'user',
  NOW(),
  NOW()
)
ON CONFLICT (id) DO UPDATE SET
  email = EXCLUDED.email,
  full_name = EXCLUDED.full_name,
  updated_at = NOW();

-- 6. Create profiles for any other users that might be missing profiles
INSERT INTO profiles (id, email, full_name, role, created_at, updated_at)
SELECT 
  au.id,
  au.email,
  COALESCE(au.raw_user_meta_data->>'full_name', 'User'),
  'user',
  NOW(),
  NOW()
FROM auth.users au
LEFT JOIN profiles p ON au.id = p.id
WHERE p.id IS NULL
ON CONFLICT (id) DO NOTHING;

-- 7. Verify profiles were created
SELECT 'Profiles after fix:' as info;
SELECT id, email, full_name, role FROM profiles ORDER BY created_at DESC;

-- 8. OPTIONAL: Re-enable foreign key constraint (only if you want strict referential integrity)
-- WARNING: This might fail if there are orphaned profiles
-- ALTER TABLE profiles ADD CONSTRAINT profiles_id_fkey 
--   FOREIGN KEY (id) REFERENCES auth.users(id) ON DELETE CASCADE;

SELECT 'Foreign key constraint fix completed!' as status;
SELECT 'Note: Foreign key constraint temporarily disabled to allow orphaned profiles' as note;
