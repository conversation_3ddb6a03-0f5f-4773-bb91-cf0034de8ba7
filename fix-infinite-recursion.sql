-- CRITICAL FIX: Infinite recursion in RLS policies
-- This error: "infinite recursion detected in policy for relation 'profiles'"
-- Run this in Supabase SQL Editor IMMEDIATELY

-- 1. DIS<PERSON><PERSON> RLS temporarily to break the recursion
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE cart_items DISABLE ROW LEVEL SECURITY;
ALTER TABLE orders DISABLE ROW LEVEL SECURITY;
ALTER TABLE order_items DISABLE ROW LEVEL SECURITY;

-- 2. DROP ALL existing policies that might be causing recursion
DROP POLICY IF EXISTS "Enable read access for authenticated users on own profile" ON profiles;
DROP POLICY IF EXISTS "Enable insert for authenticated users on own profile" ON profiles;
DROP POLICY IF EXISTS "Enable update for authenticated users on own profile" ON profiles;
DROP POLICY IF EXISTS "Allow service role full access to profiles" ON profiles;
DROP POLICY IF EXISTS "Users can read own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;
DROP POLICY IF EXISTS "Allow service role to insert profiles" ON profiles;
DROP POLICY IF EXISTS "Allow trigger to insert profiles" ON profiles;

DROP POLICY IF EXISTS "Enable all operations for authenticated users on own cart items" ON cart_items;
DROP POLICY IF EXISTS "Users can manage own cart items" ON cart_items;
DROP POLICY IF EXISTS "Users can read own cart items" ON cart_items;
DROP POLICY IF EXISTS "Users can insert own cart items" ON cart_items;
DROP POLICY IF EXISTS "Users can update own cart items" ON cart_items;
DROP POLICY IF EXISTS "Users can delete own cart items" ON cart_items;

DROP POLICY IF EXISTS "Enable all operations for authenticated users on own orders" ON orders;
DROP POLICY IF EXISTS "Users can manage own orders" ON orders;

DROP POLICY IF EXISTS "Enable read access for authenticated users on own order items" ON order_items;
DROP POLICY IF EXISTS "Users can read own order items" ON order_items;

-- 3. Create SIMPLE, non-recursive policies

-- Profiles: Simple policies without recursion
CREATE POLICY "profiles_select_policy" ON profiles
  FOR SELECT TO authenticated
  USING (id = auth.uid());

CREATE POLICY "profiles_insert_policy" ON profiles
  FOR INSERT TO authenticated
  WITH CHECK (id = auth.uid());

CREATE POLICY "profiles_update_policy" ON profiles
  FOR UPDATE TO authenticated
  USING (id = auth.uid())
  WITH CHECK (id = auth.uid());

-- Service role needs full access for triggers
CREATE POLICY "profiles_service_role_policy" ON profiles
  FOR ALL TO service_role
  USING (true)
  WITH CHECK (true);

-- Cart items: Simple policies
CREATE POLICY "cart_items_policy" ON cart_items
  FOR ALL TO authenticated
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

-- Orders: Simple policies
CREATE POLICY "orders_policy" ON orders
  FOR ALL TO authenticated
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

-- Order items: Simple policy using direct join
CREATE POLICY "order_items_policy" ON order_items
  FOR SELECT TO authenticated
  USING (
    order_id IN (
      SELECT id FROM orders WHERE user_id = auth.uid()
    )
  );

-- 4. RE-ENABLE RLS with the new simple policies
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE cart_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;

-- 5. Ensure products and categories are accessible (no RLS issues)
DROP POLICY IF EXISTS "Enable read access for all users on products" ON products;
DROP POLICY IF EXISTS "Products are viewable by everyone" ON products;
DROP POLICY IF EXISTS "Enable read access for all users on categories" ON categories;
DROP POLICY IF EXISTS "Categories are viewable by everyone" ON categories;

-- Products and categories should be readable by everyone
CREATE POLICY "products_public_read" ON products
  FOR SELECT TO authenticated, anon
  USING (true);

CREATE POLICY "categories_public_read" ON categories
  FOR SELECT TO authenticated, anon
  USING (true);

-- 6. Grant explicit permissions
GRANT USAGE ON SCHEMA public TO authenticated, anon, service_role;
GRANT SELECT ON public.products TO authenticated, anon;
GRANT SELECT ON public.categories TO authenticated, anon;
GRANT ALL ON public.profiles TO authenticated, service_role;
GRANT ALL ON public.cart_items TO authenticated;
GRANT ALL ON public.orders TO authenticated;
GRANT SELECT ON public.order_items TO authenticated;

-- 7. Create the profile for the new user (from the error log)
INSERT INTO profiles (id, email, full_name, role, created_at, updated_at)
VALUES (
  'e1cc8c46-45f8-4cee-958d-5630ef3f1838',
  '<EMAIL>',
  'gg',
  'user',
  NOW(),
  NOW()
)
ON CONFLICT (id) DO UPDATE SET
  email = EXCLUDED.email,
  full_name = EXCLUDED.full_name,
  updated_at = NOW();

-- 8. Test that policies work
SELECT 'Testing profile access...' as test;
SELECT id, email, full_name FROM profiles WHERE id = 'e1cc8c46-45f8-4cee-958d-5630ef3f1838';

SELECT 'RLS infinite recursion fix completed successfully!' as status;
