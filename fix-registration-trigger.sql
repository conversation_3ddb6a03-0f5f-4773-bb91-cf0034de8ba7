-- Fix the registration trigger that's causing "Database error saving new user"
-- This happens because the trigger tries to insert into profiles with foreign key constraint

-- 1. Check if the trigger function exists and what it does
SELECT 'Current trigger function:' as info;
SELECT routine_name, routine_definition 
FROM information_schema.routines 
WHERE routine_name = 'handle_new_user';

-- 2. Check if the trigger exists
SELECT 'Current triggers on auth.users:' as info;
SELECT trigger_name, event_manipulation, action_statement
FROM information_schema.triggers 
WHERE event_object_table = 'users' AND event_object_schema = 'auth';

-- 3. Drop the existing problematic trigger and function
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS handle_new_user();

-- 4. Create a new, safer trigger function that works without foreign key constraints
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER 
SECURITY DEFINER SET search_path = public
AS $$
BEGIN
  -- Insert into profiles table without relying on foreign key constraint
  INSERT INTO public.profiles (id, email, full_name, role, created_at, updated_at)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
    'user',
    NOW(),
    NOW()
  )
  ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    full_name = EXCLUDED.full_name,
    updated_at = NOW();
  
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error but don't fail the user creation
    RAISE LOG 'Error in handle_new_user: %, continuing anyway', SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 5. Create the trigger
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- 6. Grant necessary permissions to the function
GRANT EXECUTE ON FUNCTION handle_new_user() TO service_role;
GRANT ALL ON public.profiles TO service_role;

-- 7. Test the trigger function manually (this should work now)
SELECT 'Testing trigger function...' as test;

-- 8. Ensure profiles table is accessible
GRANT ALL ON public.profiles TO authenticated, anon, service_role;

SELECT 'Registration trigger fix completed!' as status;
SELECT 'New user registrations should work now' as note;
