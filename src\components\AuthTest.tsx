import React, { useState } from 'react';
import { useAuth } from '../context/AuthContext';

const AuthTest: React.FC = () => {
  const { user, profile, login, register, logout, loading, isAuthenticated, isAdmin } = useAuth();
  const [testEmail, setTestEmail] = useState('<EMAIL>');
  const [testPassword, setTestPassword] = useState('testpassword123');
  const [testName, setTestName] = useState('Test User');
  const [testResult, setTestResult] = useState<string>('');
  const [isRunning, setIsRunning] = useState(false);

  const runRegistrationTest = async () => {
    setIsRunning(true);
    setTestResult('');
    
    try {
      console.log('🧪 Testing registration with:', { email: testEmail, name: testName });
      
      await register(testEmail, testPassword, testName);
      setTestResult('✅ Registration test successful! Check console for details.');
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setTestResult(`❌ Registration test failed: ${errorMessage}`);
      console.error('Registration test error:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const runLoginTest = async () => {
    setIsRunning(true);
    setTestResult('');
    
    try {
      console.log('🧪 Testing login with:', { email: testEmail });
      
      await login(testEmail, testPassword);
      setTestResult('✅ Login test successful!');
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setTestResult(`❌ Login test failed: ${errorMessage}`);
      console.error('Login test error:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const runLogoutTest = () => {
    try {
      logout();
      setTestResult('✅ Logout successful!');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setTestResult(`❌ Logout failed: ${errorMessage}`);
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <h3 className="text-lg font-semibold mb-4">Authentication Test</h3>
      
      {/* Current Auth Status */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <h4 className="font-medium mb-2">Current Status:</h4>
        <div className="space-y-1 text-sm">
          <div>Loading: {loading ? '✅ Yes' : '❌ No'}</div>
          <div>Authenticated: {isAuthenticated ? '✅ Yes' : '❌ No'}</div>
          <div>Is Admin: {isAdmin ? '✅ Yes' : '❌ No'}</div>
          <div>User ID: {user?.id || 'None'}</div>
          <div>Email: {user?.email || 'None'}</div>
          <div>Profile: {profile ? `${profile.full_name} (${profile.role})` : 'None'}</div>
        </div>
      </div>

      {/* Test Credentials */}
      <div className="mb-6 space-y-3">
        <h4 className="font-medium">Test Credentials:</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          <input
            type="email"
            value={testEmail}
            onChange={(e) => setTestEmail(e.target.value)}
            placeholder="Test Email"
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm"
          />
          <input
            type="password"
            value={testPassword}
            onChange={(e) => setTestPassword(e.target.value)}
            placeholder="Test Password"
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm"
          />
          <input
            type="text"
            value={testName}
            onChange={(e) => setTestName(e.target.value)}
            placeholder="Test Name"
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm"
          />
        </div>
      </div>

      {/* Test Buttons */}
      <div className="mb-6 flex flex-wrap gap-3">
        <button
          onClick={runRegistrationTest}
          disabled={isRunning}
          className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg disabled:opacity-50 text-sm"
        >
          {isRunning ? 'Testing...' : 'Test Registration'}
        </button>
        
        <button
          onClick={runLoginTest}
          disabled={isRunning}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg disabled:opacity-50 text-sm"
        >
          {isRunning ? 'Testing...' : 'Test Login'}
        </button>
        
        <button
          onClick={runLogoutTest}
          disabled={isRunning || !isAuthenticated}
          className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg disabled:opacity-50 text-sm"
        >
          Test Logout
        </button>
      </div>

      {/* Test Results */}
      {testResult && (
        <div className="p-3 bg-gray-900 text-green-400 rounded-lg font-mono text-sm">
          {testResult}
        </div>
      )}

      {/* Warning */}
      <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg text-sm">
        <strong>⚠️ Warning:</strong> This will create real user accounts in your Supabase project. 
        Use test emails and clean up afterwards if needed.
      </div>
    </div>
  );
};

export default AuthTest;
