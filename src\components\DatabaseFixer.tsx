import React, { useState } from 'react';
import { supabase } from '../lib/supabase';

const DatabaseFixer: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<string[]>([]);

  const runDatabaseFix = async () => {
    setIsRunning(true);
    setResults([]);
    
    const logs: string[] = [];
    
    try {
      logs.push('🔧 Starting database fix...');
      
      // Step 1: Check current state
      logs.push('1. Checking profiles table...');
      const { data: profilesCheck, error: profilesError } = await supabase
        .from('profiles')
        .select('count', { count: 'exact', head: true });
      
      if (profilesError) {
        logs.push(`❌ Profiles table issue: ${profilesError.message}`);
      } else {
        logs.push(`✅ Profiles table accessible (${profilesCheck.count || 0} records)`);
      }
      
      // Step 2: Try to create the function (this might fail due to permissions)
      logs.push('2. Attempting to create handle_new_user function...');
      
      const createFunctionSQL = `
        CREATE OR REPLACE FUNCTION handle_new_user()
        RETURNS TRIGGER 
        SECURITY DEFINER SET search_path = public
        AS $$
        BEGIN
          INSERT INTO public.profiles (id, email, full_name, role, created_at, updated_at)
          VALUES (
            NEW.id, 
            NEW.email, 
            COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
            'user',
            NOW(),
            NOW()
          );
          RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
      `;
      
      const { error: functionError } = await supabase.rpc('exec_sql', { sql: createFunctionSQL });
      
      if (functionError) {
        logs.push(`⚠️ Cannot create function via app: ${functionError.message}`);
        logs.push('💡 You need to run the SQL manually in Supabase Dashboard');
      } else {
        logs.push('✅ Function created successfully');
      }
      
      // Step 3: Test manual profile creation
      logs.push('3. Testing manual profile creation...');
      
      const testId = `test-${Date.now()}`;
      const { error: insertError } = await supabase
        .from('profiles')
        .insert({
          id: testId,
          email: '<EMAIL>',
          full_name: 'Test User',
          role: 'user'
        });
      
      if (insertError) {
        logs.push(`❌ Manual profile creation failed: ${insertError.message}`);
        
        if (insertError.message.includes('foreign key')) {
          logs.push('🔍 Issue: Foreign key constraint - profiles.id must reference auth.users.id');
        }
        if (insertError.message.includes('policy')) {
          logs.push('🔍 Issue: RLS policy blocking insertion');
        }
      } else {
        logs.push('✅ Manual profile creation successful');
        
        // Clean up
        await supabase.from('profiles').delete().eq('id', testId);
        logs.push('🧹 Test data cleaned up');
      }
      
      logs.push('🎉 Database diagnostic completed!');
      
    } catch (error) {
      logs.push(`💥 Unexpected error: ${error}`);
    } finally {
      setResults(logs);
      setIsRunning(false);
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <h3 className="text-lg font-semibold mb-4">Database Fixer</h3>
      
      <div className="mb-4">
        <button
          onClick={runDatabaseFix}
          disabled={isRunning}
          className={`px-6 py-3 rounded-lg font-semibold transition-colors ${
            isRunning
              ? 'bg-gray-400 text-white cursor-not-allowed'
              : 'bg-red-600 hover:bg-red-700 text-white'
          }`}
        >
          {isRunning ? 'Running Diagnostic...' : 'Run Database Diagnostic'}
        </button>
      </div>
      
      {results.length > 0 && (
        <div className="mb-6">
          <h4 className="font-medium mb-2">Diagnostic Results:</h4>
          <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm max-h-64 overflow-y-auto">
            {results.map((result, index) => (
              <div 
                key={index} 
                className={`mb-1 ${
                  result.includes('❌') ? 'text-red-400' : 
                  result.includes('✅') ? 'text-green-400' :
                  result.includes('⚠️') ? 'text-yellow-400' :
                  result.includes('🔍') ? 'text-blue-400' :
                  'text-gray-300'
                }`}
              >
                {result}
              </div>
            ))}
          </div>
        </div>
      )}
      
      <div className="bg-blue-50 p-4 rounded-lg">
        <h4 className="font-semibold text-blue-800 mb-2">Manual Fix Instructions:</h4>
        <div className="text-blue-700 text-sm space-y-2">
          <p><strong>1. Go to your Supabase Dashboard</strong></p>
          <p><strong>2. Navigate to SQL Editor</strong></p>
          <p><strong>3. Run the SQL from either:</strong></p>
          <ul className="list-disc list-inside ml-4 space-y-1">
            <li><code>fix-auth-trigger.sql</code> (comprehensive fix)</li>
            <li><code>simple-auth-fix.sql</code> (simple fix)</li>
          </ul>
          <p><strong>4. Test registration again</strong></p>
        </div>
      </div>
      
      <div className="mt-4 bg-yellow-50 p-4 rounded-lg">
        <h4 className="font-semibold text-yellow-800 mb-2">Common Issues:</h4>
        <ul className="text-yellow-700 text-sm space-y-1">
          <li>• <strong>Migrations not applied:</strong> Database schema missing</li>
          <li>• <strong>Trigger function missing:</strong> handle_new_user() not created</li>
          <li>• <strong>RLS policies:</strong> Blocking profile creation</li>
          <li>• <strong>Permissions:</strong> service_role needs access to profiles table</li>
        </ul>
      </div>
    </div>
  );
};

export default DatabaseFixer;
