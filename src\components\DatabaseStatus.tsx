import React, { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';

interface DatabaseStats {
  products: number;
  categories: number;
  profiles: number;
  cartItems: number;
  orders: number;
  orderItems: number;
}

const DatabaseStatus: React.FC = () => {
  const [stats, setStats] = useState<DatabaseStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        
        const [
          { count: productsCount },
          { count: categoriesCount },
          { count: profilesCount },
          { count: cartItemsCount },
          { count: ordersCount },
          { count: orderItemsCount }
        ] = await Promise.all([
          supabase.from('products').select('*', { count: 'exact', head: true }),
          supabase.from('categories').select('*', { count: 'exact', head: true }),
          supabase.from('profiles').select('*', { count: 'exact', head: true }),
          supabase.from('cart_items').select('*', { count: 'exact', head: true }),
          supabase.from('orders').select('*', { count: 'exact', head: true }),
          supabase.from('order_items').select('*', { count: 'exact', head: true })
        ]);

        setStats({
          products: productsCount || 0,
          categories: categoriesCount || 0,
          profiles: profilesCount || 0,
          cartItems: cartItemsCount || 0,
          orders: ordersCount || 0,
          orderItems: orderItemsCount || 0
        });
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch database stats');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  if (loading) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h3 className="text-lg font-semibold mb-4">Database Status</h3>
        <div className="animate-pulse">Loading database stats...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h3 className="text-lg font-semibold mb-4">Database Status</h3>
        <div className="text-red-600">Error: {error}</div>
      </div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <h3 className="text-lg font-semibold mb-4">Database Status</h3>
      
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
        <div className="text-center p-4 bg-blue-50 rounded-lg">
          <div className="text-2xl font-bold text-blue-600">{stats?.products}</div>
          <div className="text-sm text-blue-800">Products</div>
        </div>
        
        <div className="text-center p-4 bg-green-50 rounded-lg">
          <div className="text-2xl font-bold text-green-600">{stats?.categories}</div>
          <div className="text-sm text-green-800">Categories</div>
        </div>
        
        <div className="text-center p-4 bg-purple-50 rounded-lg">
          <div className="text-2xl font-bold text-purple-600">{stats?.profiles}</div>
          <div className="text-sm text-purple-800">Profiles</div>
        </div>
        
        <div className="text-center p-4 bg-yellow-50 rounded-lg">
          <div className="text-2xl font-bold text-yellow-600">{stats?.cartItems}</div>
          <div className="text-sm text-yellow-800">Cart Items</div>
        </div>
        
        <div className="text-center p-4 bg-red-50 rounded-lg">
          <div className="text-2xl font-bold text-red-600">{stats?.orders}</div>
          <div className="text-sm text-red-800">Orders</div>
        </div>
        
        <div className="text-center p-4 bg-indigo-50 rounded-lg">
          <div className="text-2xl font-bold text-indigo-600">{stats?.orderItems}</div>
          <div className="text-sm text-indigo-800">Order Items</div>
        </div>
      </div>
      
      <div className="mt-4 p-3 bg-gray-50 rounded text-sm">
        <strong>Expected values:</strong> Products: 8, Categories: 6, Others: 0 (unless you've added data)
      </div>
    </div>
  );
};

export default DatabaseStatus;
