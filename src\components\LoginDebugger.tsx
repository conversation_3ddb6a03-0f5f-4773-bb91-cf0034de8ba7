import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { supabase } from '../lib/supabase';

const LoginDebugger: React.FC = () => {
  const { user, profile, loading, isAuthenticated } = useAuth();
  const [debugInfo, setDebugInfo] = useState<any>({});
  const [testResults, setTestResults] = useState<string[]>([]);

  useEffect(() => {
    const updateDebugInfo = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        
        setDebugInfo({
          authLoading: loading,
          isAuthenticated,
          hasUser: !!user,
          hasProfile: !!profile,
          hasSession: !!session,
          sessionUser: session?.user?.id || null,
          userEmail: user?.email || null,
          profileRole: profile?.role || null,
          sessionError: error?.message || null
        });
      } catch (err) {
        console.error('Debug info error:', err);
      }
    };

    updateDebugInfo();
    const interval = setInterval(updateDebugInfo, 1000); // Update every second

    return () => clearInterval(interval);
  }, [user, profile, loading, isAuthenticated]);

  const runLoginTest = async () => {
    const logs: string[] = [];
    setTestResults([]);

    try {
      logs.push('🧪 Starting login debug test...');

      // Test 1: Check current session
      logs.push('1. Checking current session...');
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError) {
        logs.push(`❌ Session error: ${sessionError.message}`);
      } else if (session) {
        logs.push(`✅ Active session found: ${session.user.email}`);
      } else {
        logs.push('ℹ️ No active session');
      }

      // Test 2: Check profiles table access
      logs.push('2. Testing profiles table access...');
      const { data: profiles, error: profilesError } = await supabase
        .from('profiles')
        .select('*')
        .limit(1);

      if (profilesError) {
        logs.push(`❌ Profiles access error: ${profilesError.message}`);
      } else {
        logs.push(`✅ Profiles accessible (${profiles?.length || 0} records)`);
      }

      // Test 3: Test auth state
      logs.push('3. Current auth state:');
      logs.push(`   - Loading: ${loading}`);
      logs.push(`   - Authenticated: ${isAuthenticated}`);
      logs.push(`   - User: ${user?.email || 'None'}`);
      logs.push(`   - Profile: ${profile?.full_name || 'None'}`);

      logs.push('🎉 Debug test completed!');

    } catch (error) {
      logs.push(`💥 Debug test error: ${error}`);
    }

    setTestResults(logs);
  };

  const forceLogout = async () => {
    try {
      await supabase.auth.signOut();
      setTestResults(['🚪 Forced logout completed']);
    } catch (error) {
      setTestResults([`❌ Logout error: ${error}`]);
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <h3 className="text-lg font-semibold mb-4">Login Debugger</h3>
      
      {/* Real-time Debug Info */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <h4 className="font-medium mb-2">Real-time Auth State:</h4>
        <div className="grid grid-cols-2 gap-2 text-sm font-mono">
          {Object.entries(debugInfo).map(([key, value]) => (
            <div key={key} className="flex justify-between">
              <span className="text-gray-600">{key}:</span>
              <span className={
                value === true ? 'text-green-600' :
                value === false ? 'text-red-600' :
                value === null ? 'text-gray-400' :
                'text-blue-600'
              }>
                {String(value)}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Test Buttons */}
      <div className="mb-4 flex gap-3">
        <button
          onClick={runLoginTest}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm"
        >
          Run Debug Test
        </button>
        
        <button
          onClick={forceLogout}
          className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg text-sm"
        >
          Force Logout
        </button>
      </div>

      {/* Test Results */}
      {testResults.length > 0 && (
        <div className="mb-4">
          <h4 className="font-medium mb-2">Test Results:</h4>
          <div className="bg-gray-900 text-green-400 p-3 rounded-lg font-mono text-sm max-h-48 overflow-y-auto">
            {testResults.map((result, index) => (
              <div 
                key={index} 
                className={`mb-1 ${
                  result.includes('❌') ? 'text-red-400' : 
                  result.includes('✅') ? 'text-green-400' :
                  result.includes('⚠️') ? 'text-yellow-400' :
                  'text-gray-300'
                }`}
              >
                {result}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="bg-blue-50 p-4 rounded-lg text-sm">
        <h4 className="font-semibold text-blue-800 mb-2">Debug Instructions:</h4>
        <ul className="text-blue-700 space-y-1">
          <li>1. Try logging in and watch the real-time state above</li>
          <li>2. Check browser console for detailed logs</li>
          <li>3. Run debug test to check system state</li>
          <li>4. Use force logout if stuck in loading state</li>
        </ul>
      </div>
    </div>
  );
};

export default LoginDebugger;
