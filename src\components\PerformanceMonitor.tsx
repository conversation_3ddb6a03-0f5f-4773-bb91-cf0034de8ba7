import React, { useRef, useEffect } from 'react';

interface PerformanceMetrics {
  renderCount: number;
  lastRenderTime: number;
  averageRenderTime: number;
  memoryUsage?: number;
}

const PerformanceMonitor: React.FC<{ componentName: string }> = ({ componentName }) => {
  const renderCountRef = useRef<number>(0);
  const renderTimesRef = useRef<number[]>([]);
  const lastWarningCountRef = useRef<number>(0);
  const mountTimeRef = useRef<number>(performance.now());

  // Increment render count on each render
  renderCountRef.current += 1;

  // Calculate current render time (time since component mounted)
  const currentTime = performance.now();
  const timeSinceMount = currentTime - mountTimeRef.current;

  // Add to render times array (keep last 10)
  renderTimesRef.current = [...renderTimesRef.current, timeSinceMount].slice(-10);

  // Calculate average render time
  const averageRenderTime = renderTimesRef.current.length > 0
    ? renderTimesRef.current.reduce((sum, time) => sum + time, 0) / renderTimesRef.current.length
    : 0;

  // Get memory usage
  const memoryUsage = (performance as any).memory?.usedJSHeapSize || undefined;

  // Create current metrics object
  const currentMetrics: PerformanceMetrics = {
    renderCount: renderCountRef.current,
    lastRenderTime: timeSinceMount,
    averageRenderTime,
    memoryUsage
  };

  // Log performance warnings (only occasionally to avoid spam)
  useEffect(() => {
    if (renderCountRef.current > lastWarningCountRef.current + 20 && averageRenderTime > 16) {
      console.warn(`⚠️ Performance Warning: ${componentName} is re-rendering frequently`, {
        renderCount: renderCountRef.current,
        averageRenderTime: averageRenderTime.toFixed(2) + 'ms'
      });
      lastWarningCountRef.current = renderCountRef.current;
    }
  }, [componentName, averageRenderTime]); // Stable dependencies

  if (process.env.NODE_ENV !== 'development') {
    return null; // Only show in development
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black bg-opacity-80 text-white p-3 rounded-lg text-xs font-mono z-50">
      <div className="font-bold text-yellow-400">{componentName}</div>
      <div>Renders: {currentMetrics.renderCount}</div>
      <div>Last: {currentMetrics.lastRenderTime.toFixed(2)}ms</div>
      <div>Avg: {currentMetrics.averageRenderTime.toFixed(2)}ms</div>
      {currentMetrics.memoryUsage && (
        <div>Memory: {(currentMetrics.memoryUsage / 1024 / 1024).toFixed(1)}MB</div>
      )}
      {currentMetrics.renderCount > 10 && (
        <div className="text-red-400">⚠️ High render count</div>
      )}
    </div>
  );
};

export default PerformanceMonitor;
