import React, { useState } from 'react';
import { testSupabaseConnection, checkEnvironmentVariables } from '../utils/supabaseTest';
import { testProductFetching } from '../utils/productTest';
import { testDatabaseTriggers, checkSupabaseProjectSettings } from '../utils/databaseDiagnostic';
import DatabaseStatus from './DatabaseStatus';
import AuthTest from './AuthTest';
import DatabaseFixer from './DatabaseFixer';
import LoginDebugger from './LoginDebugger';

const SupabaseTest: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [testResults, setTestResults] = useState<string[]>([]);

  const runTests = async () => {
    setIsRunning(true);
    setTestResults([]);
    
    // Capture console logs
    const originalLog = console.log;
    const originalError = console.error;
    const logs: string[] = [];
    
    console.log = (...args) => {
      const message = args.map(arg => 
        typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
      ).join(' ');
      logs.push(`[LOG] ${message}`);
      originalLog(...args);
    };
    
    console.error = (...args) => {
      const message = args.map(arg => 
        typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
      ).join(' ');
      logs.push(`[ERROR] ${message}`);
      originalError(...args);
    };
    
    try {
      // Check environment variables first
      checkEnvironmentVariables();

      // Check project settings
      checkSupabaseProjectSettings();

      // Run connection tests
      await testSupabaseConnection();

      // Run product-specific tests
      await testProductFetching();

      // Run database diagnostic tests
      await testDatabaseTriggers();
      
    } catch (error) {
      logs.push(`[ERROR] Test failed: ${error}`);
    } finally {
      // Restore console
      console.log = originalLog;
      console.error = originalError;
      
      setTestResults(logs);
      setIsRunning(false);
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-bold text-slate-800 mb-4">Supabase Connection Test</h2>

        <DatabaseStatus />

        <AuthTest />

        <DatabaseFixer />

        <LoginDebugger />
      
        <button
          onClick={runTests}
          disabled={isRunning}
          className={`px-6 py-3 rounded-lg font-semibold transition-colors ${
            isRunning
              ? 'bg-gray-400 text-white cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
        >
          {isRunning ? 'Running Tests...' : 'Run Detailed Connection Tests'}
        </button>
      
      {testResults.length > 0 && (
        <div className="mt-6">
          <h3 className="text-lg font-semibold text-slate-800 mb-3">Test Results:</h3>
          <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm max-h-96 overflow-y-auto">
            {testResults.map((result, index) => (
              <div 
                key={index} 
                className={`mb-1 ${
                  result.includes('[ERROR]') ? 'text-red-400' : 
                  result.includes('✅') ? 'text-green-400' :
                  result.includes('❌') ? 'text-red-400' :
                  result.includes('⚠️') ? 'text-yellow-400' :
                  'text-gray-300'
                }`}
              >
                {result}
              </div>
            ))}
          </div>
        </div>
      )}
      
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h4 className="font-semibold text-blue-800 mb-2">What this test checks:</h4>
        <ul className="text-blue-700 text-sm space-y-1">
          <li>• Environment variables configuration</li>
          <li>• Basic Supabase connection</li>
          <li>• Database table accessibility</li>
          <li>• Sample data retrieval</li>
          <li>• Table relationships (products ↔ categories)</li>
          <li>• Authentication status</li>
          <li>• Row Level Security policies</li>
        </ul>
        </div>
      </div>
    </div>
  );
};

export default SupabaseTest;
