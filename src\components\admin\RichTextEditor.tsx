import React, { useRef, useState, useEffect } from 'react';
import {
  Bold,
  Italic,
  Underline,
  Type,
  Image,
  Link,
  List,
  Quote,
  Code
} from 'lucide-react';

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

const RichTextEditor: React.FC<RichTextEditorProps> = ({ value, onChange, placeholder }) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const [showImageModal, setShowImageModal] = useState(false);
  const [showLinkModal, setShowLinkModal] = useState(false);
  const [imageUrl, setImageUrl] = useState('');
  const [imageAlt, setImageAlt] = useState('');
  const [linkText, setLinkText] = useState('');
  const [linkUrl, setLinkUrl] = useState('');
  const [isInitialized, setIsInitialized] = useState(false);

  // Convert markdown to HTML for display
  const markdownToHtml = (markdown: string) => {
    return markdown
      .replace(/^### (.*$)/gim, '<h3 class="text-xl font-semibold text-slate-900 mt-6 mb-3">$1</h3>')
      .replace(/^## (.*$)/gim, '<h2 class="text-2xl font-semibold text-slate-900 mt-8 mb-4">$1</h2>')
      .replace(/^# (.*$)/gim, '<h1 class="text-3xl font-bold text-slate-900 mt-10 mb-5">$1</h1>')
      .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold">$1</strong>')
      .replace(/\*(.*?)\*/g, '<em class="italic">$1</em>')
      .replace(/__(.*?)__/g, '<u class="underline">$1</u>')
      .replace(/`(.*?)`/g, '<code class="bg-slate-100 px-2 py-1 rounded text-sm font-mono">$1</code>')
      .replace(/!\[([^\]]*)\]\(([^)]+)\)/g, '<img src="$2" alt="$1" class="max-w-full h-auto rounded-lg my-4 shadow-sm" />')
      .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-amber-600 hover:text-amber-700 underline" target="_blank" rel="noopener noreferrer">$1</a>')
      .replace(/^> (.*$)/gim, '<blockquote class="border-l-4 border-amber-500 pl-4 py-2 my-4 italic text-slate-600 bg-slate-50 rounded-r">$1</blockquote>')
      .replace(/^- (.*$)/gim, '<li class="ml-4 mb-1">$1</li>')
      .replace(/(<li.*?<\/li>)/gs, '<ul class="list-disc list-inside mb-4">$1</ul>')
      .replace(/<span style="color: ([^"]+)">(.*?)<\/span>/g, '<span style="color: $1">$2</span>')
      .replace(/\n\n/g, '</p><p class="mb-4 text-slate-700 leading-relaxed">')
      .replace(/\n/g, '<br>');
  };

  // Convert HTML back to markdown
  const htmlToMarkdown = (html: string) => {
    return html
      .replace(/<h1[^>]*>(.*?)<\/h1>/gi, '# $1\n\n')
      .replace(/<h2[^>]*>(.*?)<\/h2>/gi, '## $1\n\n')
      .replace(/<h3[^>]*>(.*?)<\/h3>/gi, '### $1\n\n')
      .replace(/<strong[^>]*>(.*?)<\/strong>/gi, '**$1**')
      .replace(/<em[^>]*>(.*?)<\/em>/gi, '*$1*')
      .replace(/<u[^>]*>(.*?)<\/u>/gi, '__$1__')
      .replace(/<code[^>]*>(.*?)<\/code>/gi, '`$1`')
      .replace(/<img[^>]*src="([^"]*)"[^>]*alt="([^"]*)"[^>]*>/gi, '![$2]($1)')
      .replace(/<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>/gi, '[$2]($1)')
      .replace(/<blockquote[^>]*>(.*?)<\/blockquote>/gi, '> $1\n\n')
      .replace(/<li[^>]*>(.*?)<\/li>/gi, '- $1\n')
      .replace(/<ul[^>]*>(.*?)<\/ul>/gi, '$1\n')
      .replace(/<p[^>]*>(.*?)<\/p>/gi, '$1\n\n')
      .replace(/<br\s*\/?>/gi, '\n')
      .replace(/&nbsp;/gi, ' ')
      .replace(/&amp;/gi, '&')
      .replace(/&lt;/gi, '<')
      .replace(/&gt;/gi, '>')
      .trim();
  };

  // Initialize editor content
  useEffect(() => {
    if (editorRef.current && !isInitialized) {
      const htmlContent = `<div class="text-slate-700 leading-relaxed">${markdownToHtml(value)}</div>`;
      editorRef.current.innerHTML = htmlContent || `<div class="text-slate-700 leading-relaxed"><p>${placeholder || 'Start writing...'}</p></div>`;
      setIsInitialized(true);
    }
  }, [value, placeholder, isInitialized]);

  // Handle content changes
  const handleContentChange = () => {
    if (editorRef.current) {
      const htmlContent = editorRef.current.innerHTML;
      const markdownContent = htmlToMarkdown(htmlContent);
      onChange(markdownContent);
    }
  };

  // Execute formatting commands
  const execCommand = (command: string, value?: string) => {
    document.execCommand(command, false, value);
    handleContentChange();
    editorRef.current?.focus();
  };

  // Insert text at cursor
  const insertAtCursor = (text: string) => {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      range.deleteContents();
      range.insertNode(document.createTextNode(text));
      range.collapse(false);
      selection.removeAllRanges();
      selection.addRange(range);
      handleContentChange();
    }
  };

  // Handle image insertion
  const handleImageInsert = () => {
    if (imageUrl) {
      const imageMarkdown = `![${imageAlt || 'Image'}](${imageUrl})`;
      insertAtCursor(imageMarkdown);
      setImageUrl('');
      setImageAlt('');
      setShowImageModal(false);
    }
  };

  // Handle link insertion
  const handleLinkInsert = () => {
    if (linkUrl && linkText) {
      const linkMarkdown = `[${linkText}](${linkUrl})`;
      insertAtCursor(linkMarkdown);
      setLinkText('');
      setLinkUrl('');
      setShowLinkModal(false);
    }
  };

  return (
    <div className="border border-gray-300 rounded-lg overflow-hidden">
      {/* Toolbar */}
      <div className="bg-gray-50 border-b border-gray-200 p-3">
        <div className="flex flex-wrap gap-2">
          {/* Text Formatting */}
          <div className="flex gap-1 border-r border-gray-300 pr-2">
            <button
              type="button"
              onClick={() => execCommand('bold')}
              className="p-2 hover:bg-gray-200 rounded transition-colors"
              title="Bold"
            >
              <Bold className="h-4 w-4" />
            </button>
            <button
              type="button"
              onClick={() => execCommand('italic')}
              className="p-2 hover:bg-gray-200 rounded transition-colors"
              title="Italic"
            >
              <Italic className="h-4 w-4" />
            </button>
            <button
              type="button"
              onClick={() => execCommand('underline')}
              className="p-2 hover:bg-gray-200 rounded transition-colors"
              title="Underline"
            >
              <Underline className="h-4 w-4" />
            </button>
          </div>

          {/* Headings */}
          <div className="flex gap-1 border-r border-gray-300 pr-2">
            <button
              type="button"
              onClick={() => execCommand('formatBlock', 'h1')}
              className="px-3 py-2 hover:bg-gray-200 rounded transition-colors text-sm font-semibold"
              title="Heading 1"
            >
              H1
            </button>
            <button
              type="button"
              onClick={() => execCommand('formatBlock', 'h2')}
              className="px-3 py-2 hover:bg-gray-200 rounded transition-colors text-sm font-semibold"
              title="Heading 2"
            >
              H2
            </button>
            <button
              type="button"
              onClick={() => execCommand('formatBlock', 'h3')}
              className="px-3 py-2 hover:bg-gray-200 rounded transition-colors text-sm font-semibold"
              title="Heading 3"
            >
              H3
            </button>
          </div>

          {/* Content Elements */}
          <div className="flex gap-1 border-r border-gray-300 pr-2">
            <button
              type="button"
              onClick={() => execCommand('insertUnorderedList')}
              className="p-2 hover:bg-gray-200 rounded transition-colors"
              title="List"
            >
              <List className="h-4 w-4" />
            </button>
            <button
              type="button"
              onClick={() => {
                const selection = window.getSelection();
                if (selection && selection.toString()) {
                  const selectedText = selection.toString();
                  const range = selection.getRangeAt(0);
                  range.deleteContents();
                  const blockquote = document.createElement('blockquote');
                  blockquote.className = 'border-l-4 border-amber-500 pl-4 py-2 my-4 italic text-slate-600 bg-slate-50 rounded-r';
                  blockquote.textContent = selectedText;
                  range.insertNode(blockquote);
                  handleContentChange();
                }
              }}
              className="p-2 hover:bg-gray-200 rounded transition-colors"
              title="Quote"
            >
              <Quote className="h-4 w-4" />
            </button>
            <button
              type="button"
              onClick={() => {
                const selection = window.getSelection();
                if (selection && selection.toString()) {
                  const selectedText = selection.toString();
                  const range = selection.getRangeAt(0);
                  range.deleteContents();
                  const code = document.createElement('code');
                  code.className = 'bg-slate-100 px-2 py-1 rounded text-sm font-mono';
                  code.textContent = selectedText;
                  range.insertNode(code);
                  handleContentChange();
                }
              }}
              className="p-2 hover:bg-gray-200 rounded transition-colors"
              title="Code"
            >
              <Code className="h-4 w-4" />
            </button>
            <button
              type="button"
              onClick={() => setShowImageModal(true)}
              className="p-2 hover:bg-gray-200 rounded transition-colors"
              title="Image"
            >
              <Image className="h-4 w-4" />
            </button>
            <button
              type="button"
              onClick={() => setShowLinkModal(true)}
              className="p-2 hover:bg-gray-200 rounded transition-colors"
              title="Link"
            >
              <Link className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Editor Area */}
      <div className="relative">
        <div
          ref={editorRef}
          contentEditable
          onInput={handleContentChange}
          onBlur={handleContentChange}
          className="w-full p-4 min-h-[400px] focus:outline-none prose prose-lg max-w-none"
          style={{
            lineHeight: '1.6',
            fontSize: '16px',
          }}
          suppressContentEditableWarning={true}
        />
      </div>

  return (
    <div className="border border-gray-300 rounded-lg overflow-hidden">
      {/* Toolbar */}
      <div className="bg-gray-50 border-b border-gray-200 p-3">
        <div className="flex flex-wrap gap-2">
          {/* Text Formatting */}
          <div className="flex gap-1 border-r border-gray-300 pr-2">
            {toolbarButtons.slice(0, 3).map((button, index) => (
              <button
                key={index}
                type="button"
                onClick={button.action}
                className="p-2 hover:bg-gray-200 rounded transition-colors"
                title={button.label}
              >
                <button.icon className="h-4 w-4" />
              </button>
            ))}
          </div>

          {/* Headings */}
          <div className="flex gap-1 border-r border-gray-300 pr-2">
            <button
              type="button"
              onClick={toolbarButtons[3].action}
              className="px-3 py-2 hover:bg-gray-200 rounded transition-colors text-sm font-semibold"
              title="Heading 1"
            >
              H1
            </button>
            <button
              type="button"
              onClick={toolbarButtons[4].action}
              className="px-3 py-2 hover:bg-gray-200 rounded transition-colors text-sm font-semibold"
              title="Heading 2"
            >
              H2
            </button>
            <button
              type="button"
              onClick={toolbarButtons[5].action}
              className="px-3 py-2 hover:bg-gray-200 rounded transition-colors text-sm font-semibold"
              title="Heading 3"
            >
              H3
            </button>
          </div>

          {/* Content Elements */}
          <div className="flex gap-1 border-r border-gray-300 pr-2">
            {toolbarButtons.slice(6, 11).map((button, index) => (
              <button
                key={index}
                type="button"
                onClick={button.action}
                className="p-2 hover:bg-gray-200 rounded transition-colors"
                title={button.label}
              >
                <button.icon className="h-4 w-4" />
              </button>
            ))}
          </div>

          {/* Colors */}
          <div className="flex gap-1 border-r border-gray-300 pr-2">
            <div className="relative group">
              <button
                type="button"
                className="p-2 hover:bg-gray-200 rounded transition-colors"
                title="Text Color"
              >
                <Palette className="h-4 w-4" />
              </button>
              <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg p-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all z-10">
                <div className="flex gap-1">
                  {colorButtons.map((color) => (
                    <button
                      key={color.color}
                      type="button"
                      onClick={() => execCommand('foreColor', color.color)}
                      className="w-6 h-6 rounded border border-gray-300 hover:scale-110 transition-transform"
                      style={{ backgroundColor: color.color }}
                      title={color.name}
                    />
                  ))}
                </div>
              </div>
            </div>
          </div>


        </div>
      </div>

      {/* WYSIWYG Editor Area */}
      <div className="relative">
        <div
          ref={editorRef}
          contentEditable
          onInput={handleContentChange}
          onBlur={handleContentChange}
          className="w-full p-4 min-h-[400px] focus:outline-none prose max-w-none"
          style={{
            lineHeight: '1.6',
            fontSize: '16px',
          }}
          suppressContentEditableWarning={true}
          data-placeholder={placeholder}
        />

        {/* Placeholder styling */}
        <style jsx>{`
          [contenteditable]:empty:before {
            content: attr(data-placeholder);
            color: #9ca3af;
            pointer-events: none;
            position: absolute;
          }
        `}</style>
      </div>

      {/* Image Modal */}
      {showImageModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Insert Image</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Image URL *
                </label>
                <input
                  type="url"
                  value={imageUrl}
                  onChange={(e) => setImageUrl(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                  placeholder="https://example.com/image.jpg"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Alt Text
                </label>
                <input
                  type="text"
                  value={imageAlt}
                  onChange={(e) => setImageAlt(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                  placeholder="Describe the image"
                />
              </div>
            </div>
            <div className="flex justify-end space-x-3 mt-6">
              <button
                type="button"
                onClick={() => setShowImageModal(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleImageInsert}
                className="px-4 py-2 bg-amber-500 hover:bg-amber-600 text-white rounded-lg"
              >
                Insert Image
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Link Modal */}
      {showLinkModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Insert Link</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Link Text *
                </label>
                <input
                  type="text"
                  value={linkText}
                  onChange={(e) => setLinkText(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                  placeholder="Click here"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  URL *
                </label>
                <input
                  type="url"
                  value={linkUrl}
                  onChange={(e) => setLinkUrl(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                  placeholder="https://example.com"
                />
              </div>
            </div>
            <div className="flex justify-end space-x-3 mt-6">
              <button
                type="button"
                onClick={() => setShowLinkModal(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleLinkInsert}
                className="px-4 py-2 bg-amber-500 hover:bg-amber-600 text-white rounded-lg"
              >
                Insert Link
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RichTextEditor;
