import React, { useRef, useState, useEffect } from 'react';
import { 
  Bold, 
  Italic, 
  Underline, 
  Type, 
  Image, 
  Link, 
  List, 
  Quote, 
  Code
} from 'lucide-react';

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

const SimpleRichTextEditor: React.FC<RichTextEditorProps> = ({ value, onChange, placeholder }) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const [showImageModal, setShowImageModal] = useState(false);
  const [showLinkModal, setShowLinkModal] = useState(false);
  const [imageUrl, setImageUrl] = useState('');
  const [imageAlt, setImageAlt] = useState('');
  const [linkText, setLinkText] = useState('');
  const [linkUrl, setLinkUrl] = useState('');
  const [activeFormats, setActiveFormats] = useState<Set<string>>(new Set());
  const [savedSelection, setSavedSelection] = useState<Range | null>(null);

  // Convert markdown to HTML for display
  const markdownToHtml = (markdown: string) => {
    return markdown
      .replace(/^### (.*$)/gim, '<h3 class="text-xl font-semibold text-slate-900 mt-6 mb-3">$1</h3>')
      .replace(/^## (.*$)/gim, '<h2 class="text-2xl font-semibold text-slate-900 mt-8 mb-4">$1</h2>')
      .replace(/^# (.*$)/gim, '<h1 class="text-3xl font-bold text-slate-900 mt-10 mb-5">$1</h1>')
      .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold">$1</strong>')
      .replace(/\*(.*?)\*/g, '<em class="italic">$1</em>')
      .replace(/__(.*?)__/g, '<u class="underline">$1</u>')
      .replace(/`(.*?)`/g, '<code class="bg-slate-100 px-2 py-1 rounded text-sm font-mono">$1</code>')
      .replace(/!\[([^\]]*)\]\(([^)]+)\)/g, '<img src="$2" alt="$1" class="max-w-full h-auto rounded-lg my-4 shadow-sm" />')
      .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-amber-600 hover:text-amber-700 underline" target="_blank" rel="noopener noreferrer">$1</a>')
      .replace(/^> (.*$)/gim, '<blockquote class="border-l-4 border-amber-500 pl-4 py-2 my-4 italic text-slate-600 bg-slate-50 rounded-r">$1</blockquote>')
      .replace(/^- (.*$)/gim, '<li class="ml-4 mb-1">$1</li>')
      .replace(/(<li.*?<\/li>)/gs, '<ul class="list-disc list-inside mb-4">$1</ul>')
      .replace(/\n\n/g, '</p><p class="mb-4 text-slate-700 leading-relaxed">')
      .replace(/\n/g, '<br>');
  };

  // Convert HTML back to markdown
  const htmlToMarkdown = (html: string) => {
    let result = html;

    // Clean up nested formatting first to avoid double conversion
    result = result.replace(/<(strong|b)[^>]*><(strong|b)[^>]*>(.*?)<\/(strong|b)><\/(strong|b)>/gi, '<strong>$3</strong>');
    result = result.replace(/<(em|i)[^>]*><(em|i)[^>]*>(.*?)<\/(em|i)><\/(em|i)>/gi, '<em>$3</em>');
    result = result.replace(/<u[^>]*><u[^>]*>(.*?)<\/u><\/u>/gi, '<u>$1</u>');

    // Convert to markdown
    result = result
      .replace(/<h1[^>]*>(.*?)<\/h1>/gi, '# $1\n\n')
      .replace(/<h2[^>]*>(.*?)<\/h2>/gi, '## $1\n\n')
      .replace(/<h3[^>]*>(.*?)<\/h3>/gi, '### $1\n\n')
      .replace(/<strong[^>]*>(.*?)<\/strong>/gi, '**$1**')
      .replace(/<b[^>]*>(.*?)<\/b>/gi, '**$1**')
      .replace(/<em[^>]*>(.*?)<\/em>/gi, '*$1*')
      .replace(/<i[^>]*>(.*?)<\/i>/gi, '*$1*')
      .replace(/<u[^>]*>(.*?)<\/u>/gi, '__$1__')
      .replace(/<code[^>]*>(.*?)<\/code>/gi, '`$1`')
      .replace(/<img[^>]*src="([^"]*)"[^>]*alt="([^"]*)"[^>]*>/gi, '![$2]($1)')
      .replace(/<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>/gi, '[$2]($1)')
      .replace(/<blockquote[^>]*>(.*?)<\/blockquote>/gi, '> $1\n\n')
      .replace(/<li[^>]*>(.*?)<\/li>/gi, '- $1\n')
      .replace(/<ul[^>]*>(.*?)<\/ul>/gi, '$1\n')
      .replace(/<p[^>]*>(.*?)<\/p>/gi, '$1\n\n')
      .replace(/<br\s*\/?>/gi, '\n')
      .replace(/<div[^>]*>(.*?)<\/div>/gi, '$1\n')
      .replace(/&nbsp;/gi, ' ')
      .replace(/&amp;/gi, '&')
      .replace(/&lt;/gi, '<')
      .replace(/&gt;/gi, '>')
      .trim();

    // Clean up multiple consecutive markdown markers
    result = result.replace(/\*{3,}/g, '**'); // Replace *** or more with **
    result = result.replace(/_{3,}/g, '__'); // Replace ___ or more with __

    return result;
  };

  // Initialize editor content
  useEffect(() => {
    if (editorRef.current && value) {
      const htmlContent = markdownToHtml(value);
      editorRef.current.innerHTML = htmlContent || '';
    }
  }, []);



  // Handle content changes
  const handleContentChange = () => {
    if (editorRef.current) {
      const htmlContent = editorRef.current.innerHTML;
      const markdownContent = htmlToMarkdown(htmlContent);
      onChange(markdownContent);
    }
  };



  // Check if a format is currently active
  const isFormatActive = (command: string): boolean => {
    try {
      return document.queryCommandState(command);
    } catch (e) {
      return false;
    }
  };

  // Check if cursor is currently in a list
  const isInList = (): boolean => {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      let element = selection.anchorNode;

      // If text node, get parent element
      if (element?.nodeType === Node.TEXT_NODE) {
        element = element.parentElement;
      }

      // Walk up the DOM to find list elements
      while (element && element !== editorRef.current) {
        const tagName = (element as Element).tagName?.toLowerCase();
        if (tagName === 'ul' || tagName === 'ol' || tagName === 'li') {
          return true;
        }
        element = element.parentElement;
      }
    }
    return false;
  };

  // Get current block format (heading level)
  const getCurrentBlockFormat = (): string => {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      let element = selection.anchorNode;

      // If text node, get parent element
      if (element?.nodeType === Node.TEXT_NODE) {
        element = element.parentElement;
      }

      // Walk up the DOM to find heading or block element
      while (element && element !== editorRef.current) {
        const tagName = (element as Element).tagName?.toLowerCase();
        if (tagName === 'h1') return 'h1';
        if (tagName === 'h2') return 'h2';
        if (tagName === 'h3') return 'h3';
        element = element.parentElement;
      }
    }
    return 'p'; // default to paragraph
  };

  // Get current inline heading format (for selected text or cursor position)
  const getCurrentInlineFormat = (): string | null => {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      let element = selection.anchorNode;

      // If text node, get parent element
      if (element?.nodeType === Node.TEXT_NODE) {
        element = element.parentElement;
      }

      // Check if we're inside a span with heading classes
      while (element && element !== editorRef.current) {
        if ((element as Element).tagName?.toLowerCase() === 'span') {
          const className = (element as Element).className;
          if (className.includes('text-3xl')) return 'h1';
          if (className.includes('text-2xl')) return 'h2';
          if (className.includes('text-xl')) return 'h3';
        }
        element = element.parentElement;
      }
    }
    return null;
  };

  // Check if text is selected
  const hasTextSelection = (): boolean => {
    const selection = window.getSelection();
    return selection ? selection.toString().trim().length > 0 : false;
  };

  // Update active formats based on cursor position
  const updateActiveFormats = () => {
    const formats = new Set<string>();

    if (isFormatActive('bold')) formats.add('bold');
    if (isFormatActive('italic')) formats.add('italic');
    if (isFormatActive('underline')) formats.add('underline');

    // Check for list formatting
    if (isInList()) {
      formats.add('list');
    }

    // Check for block-level headings
    const blockFormat = getCurrentBlockFormat();
    if (blockFormat !== 'p') {
      formats.add(blockFormat);
    }

    // Check for inline heading styles
    const inlineFormat = getCurrentInlineFormat();
    if (inlineFormat) {
      formats.add(inlineFormat);
    }

    setActiveFormats(formats);
  };

  // Execute formatting commands with toggle behavior
  const execCommand = (command: string, value?: string) => {
    document.execCommand(command, false, value);
    handleContentChange();
    updateActiveFormats();
    editorRef.current?.focus();
  };

  // Handle heading formatting with toggle behavior
  const applyHeading = (level: 'h1' | 'h2' | 'h3') => {
    if (!editorRef.current) return;

    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const hasSelection = hasTextSelection();
    const currentBlockFormat = getCurrentBlockFormat();
    const currentInlineFormat = getCurrentInlineFormat();

    if (hasSelection) {
      // Handle selected text (inline formatting)
      if (currentInlineFormat === level) {
        // Remove inline heading style
        removeInlineHeadingStyle();
      } else {
        // Apply or change inline heading style
        applyInlineHeadingStyle(level);
      }
    } else {
      // Handle entire paragraph (block formatting)
      if (currentBlockFormat === level) {
        // Convert heading back to paragraph
        applyBlockHeading('p');
      } else {
        // Apply block heading
        applyBlockHeading(level);
      }
    }

    handleContentChange();
    setTimeout(updateActiveFormats, 100);
    editorRef.current?.focus();
  };

  // Apply heading style to selected text only (inline)
  const applyInlineHeadingStyle = (level: 'h1' | 'h2' | 'h3') => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    const selectedText = selection.toString();

    if (selectedText) {
      // Check if selection is already inside a heading span
      let parentSpan = range.commonAncestorContainer;
      if (parentSpan.nodeType === Node.TEXT_NODE) {
        parentSpan = parentSpan.parentElement;
      }

      // If already in a heading span, replace it
      if (parentSpan && (parentSpan as Element).tagName === 'SPAN' &&
          (parentSpan as Element).className.includes('text-')) {
        (parentSpan as Element).className = getInlineHeadingClasses(level);
        selection.removeAllRanges();
        return;
      }

      // Create a new span with heading styles
      const span = document.createElement('span');
      span.className = getInlineHeadingClasses(level);
      span.textContent = selectedText;

      // Replace selected text with styled span
      range.deleteContents();
      range.insertNode(span);

      // Position cursor after the span
      const newRange = document.createRange();
      newRange.setStartAfter(span);
      newRange.collapse(true);
      selection.removeAllRanges();
      selection.addRange(newRange);
    }
  };

  // Remove inline heading style from selected text
  const removeInlineHeadingStyle = () => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    let element = range.commonAncestorContainer;

    // If text node, get parent element
    if (element.nodeType === Node.TEXT_NODE) {
      element = element.parentElement;
    }

    // Find the heading span
    while (element && element !== editorRef.current) {
      if ((element as Element).tagName === 'SPAN' &&
          (element as Element).className.includes('text-')) {
        // Replace span with its text content
        const textNode = document.createTextNode((element as Element).textContent || '');
        element.parentElement?.replaceChild(textNode, element);

        // Position cursor after the text
        const newRange = document.createRange();
        newRange.setStartAfter(textNode);
        newRange.collapse(true);
        selection.removeAllRanges();
        selection.addRange(newRange);
        break;
      }
      element = element.parentElement;
    }
  };

  // Apply block-level heading to entire paragraph
  const applyBlockHeading = (level: 'h1' | 'h2' | 'h3' | 'p') => {
    try {
      if (level === 'p') {
        // Convert to paragraph
        document.execCommand('formatBlock', false, 'div');
        setTimeout(() => {
          if (getCurrentBlockFormat() !== 'p') {
            document.execCommand('formatBlock', false, 'p');
          }
        }, 0);
      } else {
        // Apply the heading
        document.execCommand('formatBlock', false, level);

        // Fallback: if formatBlock didn't work, try manual approach
        setTimeout(() => {
          if (getCurrentBlockFormat() !== level) {
            applyHeadingManually(level);
          }
        }, 0);
      }
    } catch (e) {
      // If formatBlock fails, use manual approach
      if (level !== 'p') {
        applyHeadingManually(level);
      }
    }
  };

  // Manual heading application as fallback
  const applyHeadingManually = (level: 'h1' | 'h2' | 'h3') => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    let element = range.commonAncestorContainer;

    // Find the block element to replace
    while (element && element.nodeType !== Node.ELEMENT_NODE) {
      element = element.parentNode;
    }

    // If we're in a text node, get the parent block
    if (element && element !== editorRef.current) {
      let blockElement = element as Element;
      while (blockElement && blockElement.parentElement !== editorRef.current) {
        blockElement = blockElement.parentElement!;
      }

      if (blockElement) {
        // Create new heading element
        const newHeading = document.createElement(level);
        newHeading.innerHTML = blockElement.innerHTML;
        newHeading.className = getHeadingClasses(level);

        // Replace the old element
        blockElement.parentElement?.replaceChild(newHeading, blockElement);

        // Restore selection
        const newRange = document.createRange();
        newRange.selectNodeContents(newHeading);
        newRange.collapse(false);
        selection.removeAllRanges();
        selection.addRange(newRange);
      }
    }
  };

  // Get CSS classes for heading levels (block)
  const getHeadingClasses = (level: 'h1' | 'h2' | 'h3'): string => {
    switch (level) {
      case 'h1':
        return 'text-3xl font-bold text-slate-900 mt-10 mb-5';
      case 'h2':
        return 'text-2xl font-semibold text-slate-900 mt-8 mb-4';
      case 'h3':
        return 'text-xl font-semibold text-slate-900 mt-6 mb-3';
      default:
        return '';
    }
  };

  // Get CSS classes for inline heading styles
  const getInlineHeadingClasses = (level: 'h1' | 'h2' | 'h3'): string => {
    switch (level) {
      case 'h1':
        return 'text-3xl font-bold text-slate-900';
      case 'h2':
        return 'text-2xl font-semibold text-slate-900';
      case 'h3':
        return 'text-xl font-semibold text-slate-900';
      default:
        return '';
    }
  };

  // Handle cursor position changes to update active formats
  const handleSelectionChange = () => {
    updateActiveFormats();
  };

  // Add event listener for selection changes
  useEffect(() => {
    document.addEventListener('selectionchange', handleSelectionChange);
    return () => {
      document.removeEventListener('selectionchange', handleSelectionChange);
    };
  }, []);

  // Save current selection before opening modals
  const saveSelection = () => {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      setSavedSelection(selection.getRangeAt(0).cloneRange());
    }
  };

  // Restore saved selection
  const restoreSelection = () => {
    if (savedSelection && editorRef.current) {
      const selection = window.getSelection();
      if (selection) {
        selection.removeAllRanges();
        selection.addRange(savedSelection);
        editorRef.current.focus();
      }
    }
  };

  // Insert text at cursor (using saved selection if available)
  const insertAtCursor = (text: string) => {
    let range = savedSelection;

    if (!range) {
      const selection = window.getSelection();
      if (selection && selection.rangeCount > 0) {
        range = selection.getRangeAt(0);
      }
    }

    if (range && editorRef.current) {
      // Ensure the range is still valid
      try {
        const selection = window.getSelection();
        if (selection) {
          selection.removeAllRanges();
          selection.addRange(range);

          range.deleteContents();
          range.insertNode(document.createTextNode(text));
          range.collapse(false);

          selection.removeAllRanges();
          selection.addRange(range);

          handleContentChange();
          editorRef.current.focus();
        }
      } catch (e) {
        // If range is invalid, append to end
        console.warn('Range invalid, appending to end:', e);
        editorRef.current.appendChild(document.createTextNode(text));
        handleContentChange();
      }
    }

    // Clear saved selection
    setSavedSelection(null);
  };

  // Handle list formatting with toggle behavior
  const applyList = () => {
    const currentlyInList = isInList();

    try {
      if (currentlyInList) {
        // Remove list formatting - convert back to paragraph
        document.execCommand('insertUnorderedList', false);
        // Additional cleanup if needed
        setTimeout(() => {
          if (isInList()) {
            // Fallback: manual list removal
            removeListFormatting();
          }
        }, 0);
      } else {
        // Apply list formatting
        document.execCommand('insertUnorderedList', false);
        // Fallback if command didn't work
        setTimeout(() => {
          if (!isInList()) {
            createListManually();
          }
        }, 0);
      }

      handleContentChange();
      setTimeout(updateActiveFormats, 100);
      editorRef.current?.focus();
    } catch (e) {
      // Fallback based on current state
      if (currentlyInList) {
        removeListFormatting();
      } else {
        createListManually();
      }
    }
  };

  // Manual list creation as fallback
  const createListManually = () => {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const selectedText = selection.toString() || 'List item';

      const listItem = document.createElement('li');
      listItem.textContent = selectedText;

      const list = document.createElement('ul');
      list.appendChild(listItem);

      range.deleteContents();
      range.insertNode(list);

      // Position cursor in the list item
      const newRange = document.createRange();
      newRange.selectNodeContents(listItem);
      newRange.collapse(false);
      selection.removeAllRanges();
      selection.addRange(newRange);

      handleContentChange();
      updateActiveFormats();
    }
  };

  // Remove list formatting manually
  const removeListFormatting = () => {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      let element = selection.anchorNode;

      // If text node, get parent element
      if (element?.nodeType === Node.TEXT_NODE) {
        element = element.parentElement;
      }

      // Find the list item or list
      while (element && element !== editorRef.current) {
        if ((element as Element).tagName?.toLowerCase() === 'li') {
          // Convert list item to paragraph
          const p = document.createElement('p');
          p.innerHTML = (element as Element).innerHTML;
          element.parentElement?.replaceChild(p, element);

          // Position cursor in the new paragraph
          const newRange = document.createRange();
          newRange.selectNodeContents(p);
          newRange.collapse(false);
          selection.removeAllRanges();
          selection.addRange(newRange);
          break;
        }
        element = element.parentElement;
      }

      handleContentChange();
      updateActiveFormats();
    }
  };

  // Handle image insertion
  const handleImageInsert = () => {
    if (imageUrl) {
      // Insert HTML image element directly for immediate visual feedback
      let range = savedSelection;

      if (!range) {
        const selection = window.getSelection();
        if (selection && selection.rangeCount > 0) {
          range = selection.getRangeAt(0);
        }
      }

      if (range && editorRef.current) {
        try {
          const selection = window.getSelection();
          if (selection) {
            selection.removeAllRanges();
            selection.addRange(range);

            // Create image element
            const img = document.createElement('img');
            img.src = imageUrl;
            img.alt = imageAlt || 'Image';
            img.className = 'max-w-full h-auto rounded-lg my-4 shadow-sm';
            img.style.display = 'block';

            range.deleteContents();
            range.insertNode(img);

            // Position cursor after image
            const newRange = document.createRange();
            newRange.setStartAfter(img);
            newRange.collapse(true);
            selection.removeAllRanges();
            selection.addRange(newRange);

            handleContentChange();
            editorRef.current.focus();
          }
        } catch (e) {
          console.warn('Image insertion failed:', e);
        }
      }

      setImageUrl('');
      setImageAlt('');
      setShowImageModal(false);
      setSavedSelection(null);
    }
  };

  // Handle link insertion
  const handleLinkInsert = () => {
    if (linkUrl && linkText) {
      // Insert HTML link element directly for immediate visual feedback
      let range = savedSelection;

      if (!range) {
        const selection = window.getSelection();
        if (selection && selection.rangeCount > 0) {
          range = selection.getRangeAt(0);
        }
      }

      if (range && editorRef.current) {
        try {
          const selection = window.getSelection();
          if (selection) {
            selection.removeAllRanges();
            selection.addRange(range);

            // Create link element
            const link = document.createElement('a');
            link.href = linkUrl;
            link.textContent = linkText;
            link.className = 'text-amber-600 hover:text-amber-700 underline';
            link.target = '_blank';
            link.rel = 'noopener noreferrer';

            range.deleteContents();
            range.insertNode(link);

            // Position cursor after link
            const newRange = document.createRange();
            newRange.setStartAfter(link);
            newRange.collapse(true);
            selection.removeAllRanges();
            selection.addRange(newRange);

            handleContentChange();
            editorRef.current.focus();
          }
        } catch (e) {
          console.warn('Link insertion failed:', e);
        }
      }

      setLinkText('');
      setLinkUrl('');
      setShowLinkModal(false);
      setSavedSelection(null);
    }
  };

  return (
    <div className="border border-gray-300 rounded-lg overflow-hidden">
      {/* Toolbar */}
      <div className="bg-gray-50 border-b border-gray-200 p-3">
        <div className="flex flex-wrap gap-2">
          {/* Text Formatting */}
          <div className="flex gap-1 border-r border-gray-300 pr-2">
            <button
              type="button"
              onClick={() => execCommand('bold')}
              className={`p-2 rounded transition-colors ${
                activeFormats.has('bold')
                  ? 'bg-amber-100 text-amber-700 hover:bg-amber-200'
                  : 'hover:bg-gray-200'
              }`}
              title="Bold (Ctrl+B)"
            >
              <Bold className="h-4 w-4" />
            </button>
            <button
              type="button"
              onClick={() => execCommand('italic')}
              className={`p-2 rounded transition-colors ${
                activeFormats.has('italic')
                  ? 'bg-amber-100 text-amber-700 hover:bg-amber-200'
                  : 'hover:bg-gray-200'
              }`}
              title="Italic (Ctrl+I)"
            >
              <Italic className="h-4 w-4" />
            </button>
            <button
              type="button"
              onClick={() => execCommand('underline')}
              className={`p-2 rounded transition-colors ${
                activeFormats.has('underline')
                  ? 'bg-amber-100 text-amber-700 hover:bg-amber-200'
                  : 'hover:bg-gray-200'
              }`}
              title="Underline (Ctrl+U)"
            >
              <Underline className="h-4 w-4" />
            </button>
          </div>

          {/* Headings */}
          <div className="flex gap-1 border-r border-gray-300 pr-2">
            <button
              type="button"
              onClick={() => applyHeading('h1')}
              className={`px-3 py-2 rounded transition-colors text-sm font-semibold ${
                activeFormats.has('h1')
                  ? 'bg-amber-100 text-amber-700 hover:bg-amber-200'
                  : 'hover:bg-gray-200'
              }`}
              title="Heading 1 - Large title"
            >
              H1
            </button>
            <button
              type="button"
              onClick={() => applyHeading('h2')}
              className={`px-3 py-2 rounded transition-colors text-sm font-semibold ${
                activeFormats.has('h2')
                  ? 'bg-amber-100 text-amber-700 hover:bg-amber-200'
                  : 'hover:bg-gray-200'
              }`}
              title="Heading 2 - Section title"
            >
              H2
            </button>
            <button
              type="button"
              onClick={() => applyHeading('h3')}
              className={`px-3 py-2 rounded transition-colors text-sm font-semibold ${
                activeFormats.has('h3')
                  ? 'bg-amber-100 text-amber-700 hover:bg-amber-200'
                  : 'hover:bg-gray-200'
              }`}
              title="Heading 3 - Subsection title"
            >
              H3
            </button>
          </div>

          {/* Content Elements */}
          <div className="flex gap-1 border-r border-gray-300 pr-2">
            <button
              type="button"
              onClick={applyList}
              className={`p-2 rounded transition-colors ${
                activeFormats.has('list')
                  ? 'bg-amber-100 text-amber-700 hover:bg-amber-200'
                  : 'hover:bg-gray-200'
              }`}
              title="Bulleted List"
            >
              <List className="h-4 w-4" />
            </button>
            <button
              type="button"
              onClick={() => {
                saveSelection();
                setShowImageModal(true);
              }}
              className="p-2 hover:bg-gray-200 rounded transition-colors"
              title="Insert Image"
            >
              <Image className="h-4 w-4" />
            </button>
            <button
              type="button"
              onClick={() => {
                // Save selection first
                saveSelection();

                // Pre-fill link text with selected text if any
                const selection = window.getSelection();
                const selectedText = selection?.toString().trim() || '';
                if (selectedText) {
                  setLinkText(selectedText);
                }
                setShowLinkModal(true);
              }}
              className="p-2 hover:bg-gray-200 rounded transition-colors"
              title="Insert Link"
            >
              <Link className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Editor Area */}
      <div className="relative">
        <div
          ref={editorRef}
          contentEditable
          onInput={handleContentChange}
          onBlur={handleContentChange}
          onKeyUp={updateActiveFormats}
          onMouseUp={updateActiveFormats}
          onFocus={updateActiveFormats}
          onKeyDown={(e) => {
            // Handle keyboard shortcuts
            if (e.ctrlKey || e.metaKey) {
              switch (e.key.toLowerCase()) {
                case 'b':
                  e.preventDefault();
                  execCommand('bold');
                  break;
                case 'i':
                  e.preventDefault();
                  execCommand('italic');
                  break;
                case 'u':
                  e.preventDefault();
                  execCommand('underline');
                  break;
              }
            }
          }}
          className="w-full p-4 min-h-[400px] focus:outline-none prose prose-lg max-w-none"
          style={{
            lineHeight: '1.6',
            fontSize: '16px',
          }}
          suppressContentEditableWarning={true}
          data-placeholder={placeholder || 'Start writing your blog post...'}
        />
        
        {/* Placeholder and heading styling */}
        <style jsx>{`
          [contenteditable]:empty:before {
            content: attr(data-placeholder);
            color: #9ca3af;
            pointer-events: none;
            position: absolute;
          }

          [contenteditable] h1 {
            font-size: 1.875rem;
            font-weight: 700;
            color: #0f172a;
            margin-top: 2.5rem;
            margin-bottom: 1.25rem;
            line-height: 1.2;
          }

          [contenteditable] h2 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #0f172a;
            margin-top: 2rem;
            margin-bottom: 1rem;
            line-height: 1.3;
          }

          [contenteditable] h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: #0f172a;
            margin-top: 1.5rem;
            margin-bottom: 0.75rem;
            line-height: 1.4;
          }

          [contenteditable] p {
            margin-bottom: 1rem;
            color: #475569;
            line-height: 1.6;
          }

          /* Inline heading styles */
          [contenteditable] span.text-3xl {
            font-size: 1.875rem;
            font-weight: 700;
            color: #0f172a;
            line-height: 1.2;
          }

          [contenteditable] span.text-2xl {
            font-size: 1.5rem;
            font-weight: 600;
            color: #0f172a;
            line-height: 1.3;
          }

          [contenteditable] span.text-xl {
            font-size: 1.25rem;
            font-weight: 600;
            color: #0f172a;
            line-height: 1.4;
          }

          /* List styles */
          [contenteditable] ul {
            list-style-type: disc;
            list-style-position: inside;
            margin-bottom: 1rem;
            padding-left: 1rem;
          }

          [contenteditable] li {
            margin-bottom: 0.25rem;
            color: #475569;
            line-height: 1.6;
          }

          /* Link styles */
          [contenteditable] a {
            color: #d97706;
            text-decoration: underline;
            transition: color 0.2s;
          }

          [contenteditable] a:hover {
            color: #b45309;
          }

          /* Image styles */
          [contenteditable] img {
            max-width: 100%;
            height: auto;
            border-radius: 0.5rem;
            margin: 1rem 0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          }
        `}</style>
      </div>

      {/* Image Modal */}
      {showImageModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Insert Image</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Image URL *
                </label>
                <input
                  type="url"
                  value={imageUrl}
                  onChange={(e) => setImageUrl(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                  placeholder="https://example.com/image.jpg"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Alt Text
                </label>
                <input
                  type="text"
                  value={imageAlt}
                  onChange={(e) => setImageAlt(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                  placeholder="Describe the image"
                />
              </div>
            </div>
            <div className="flex justify-end space-x-3 mt-6">
              <button
                type="button"
                onClick={() => {
                  setShowImageModal(false);
                  setSavedSelection(null);
                }}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleImageInsert}
                className="px-4 py-2 bg-amber-500 hover:bg-amber-600 text-white rounded-lg"
              >
                Insert Image
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Link Modal */}
      {showLinkModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Insert Link</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Link Text *
                </label>
                <input
                  type="text"
                  value={linkText}
                  onChange={(e) => setLinkText(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                  placeholder="Click here"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  URL *
                </label>
                <input
                  type="url"
                  value={linkUrl}
                  onChange={(e) => setLinkUrl(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                  placeholder="https://example.com"
                />
              </div>
            </div>
            <div className="flex justify-end space-x-3 mt-6">
              <button
                type="button"
                onClick={() => {
                  setShowLinkModal(false);
                  setSavedSelection(null);
                }}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleLinkInsert}
                className="px-4 py-2 bg-amber-500 hover:bg-amber-600 text-white rounded-lg"
              >
                Insert Link
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SimpleRichTextEditor;
