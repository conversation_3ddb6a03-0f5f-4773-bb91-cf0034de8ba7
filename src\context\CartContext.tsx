import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { supabase, type CartItem as SupabaseCartItem, type Product } from '../lib/supabase';
import { useAuth } from './AuthContext';

interface CartItemWithProduct extends SupabaseCartItem {
  products: Product;
}

interface CartContextType {
  cartItems: CartItemWithProduct[];
  loading: boolean;
  error: string | null;
  cartTotal: number;
  cartItemCount: number;
  addToCart: (productId: string, size: string, color?: string, quantity?: number) => Promise<void>;
  updateQuantity: (cartItemId: string, quantity: number) => Promise<void>;
  removeFromCart: (cartItemId: string) => Promise<void>;
  clearCart: () => Promise<void>;
  refetch: () => Promise<void>;
}

const CartContext = createContext<CartContextType | null>(null);

export const CartProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [cartItems, setCartItems] = useState<CartItemWithProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  const fetchCartItems = useCallback(async () => {
    if (!user) {
      console.log('🛒 No user - clearing cart items');
      setCartItems([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      console.log('🛒 [Context] Fetching cart items for user:', user.id);
      const startTime = performance.now();

      const { data, error } = await supabase
        .from('cart_items')
        .select(`
          *,
          products (*)
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      const endTime = performance.now();
      const fetchTime = (endTime - startTime).toFixed(2);

      if (error) {
        console.error('❌ [Context] Cart items fetch failed:', {
          error: error,
          code: error.code,
          message: error.message,
          fetchTime: `${fetchTime}ms`
        });
        throw error;
      }

      console.log('✅ [Context] Cart items fetched successfully:', {
        count: data?.length || 0,
        fetchTime: `${fetchTime}ms`
      });

      setCartItems(data || []);
      setError(null);
    } catch (err) {
      console.error('❌ [Context] Cart fetch error:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  }, [user]);

  useEffect(() => {
    fetchCartItems();
  }, [fetchCartItems]);

  const addToCart = useCallback(async (productId: string, size: string, color?: string, quantity: number = 1) => {
    if (!user) {
      throw new Error('User must be logged in to add items to cart');
    }

    try {
      console.log('🛒 [Context] Adding item to cart:', { productId, size, color, quantity });
      const startTime = performance.now();

      // Check if item already exists in cart
      const { data: existingItem } = await supabase
        .from('cart_items')
        .select('*')
        .eq('user_id', user.id)
        .eq('product_id', productId)
        .eq('size', size)
        .eq('color', color || null)
        .single();

      if (existingItem) {
        console.log('🔄 [Context] Updating existing cart item quantity');
        // Update quantity if item exists
        const { error } = await supabase
          .from('cart_items')
          .update({ quantity: existingItem.quantity + quantity })
          .eq('id', existingItem.id);

        if (error) throw error;
      } else {
        console.log('➕ [Context] Adding new cart item');
        // Insert new item
        const { error } = await supabase
          .from('cart_items')
          .insert({
            user_id: user.id,
            product_id: productId,
            size,
            color,
            quantity
          });

        if (error) throw error;
      }

      const endTime = performance.now();
      const addTime = (endTime - startTime).toFixed(2);
      console.log('✅ [Context] Item added to cart successfully, refreshing...', { addTime: `${addTime}ms` });

      // Immediately refresh cart items to update UI everywhere
      await fetchCartItems();

      console.log('🔄 [Context] Cart data refreshed globally after add');
    } catch (err) {
      console.error('❌ [Context] Failed to add item to cart:', err);
      throw new Error(err instanceof Error ? err.message : 'Failed to add item to cart');
    }
  }, [user, fetchCartItems]);

  const updateQuantity = useCallback(async (cartItemId: string, quantity: number) => {
    try {
      console.log('🛒 [Context] Updating cart item quantity:', { cartItemId, quantity });

      if (quantity <= 0) {
        console.log('🗑️ [Context] Quantity is 0, removing item instead');
        await removeFromCart(cartItemId);
        return;
      }

      const { error } = await supabase
        .from('cart_items')
        .update({ quantity })
        .eq('id', cartItemId);

      if (error) throw error;

      console.log('✅ [Context] Quantity updated, refreshing cart data...');
      await fetchCartItems();
      console.log('🔄 [Context] Cart data refreshed globally after quantity update');
    } catch (err) {
      console.error('❌ [Context] Failed to update quantity:', err);
      throw new Error(err instanceof Error ? err.message : 'Failed to update quantity');
    }
  }, [fetchCartItems]);

  const removeFromCart = useCallback(async (cartItemId: string) => {
    try {
      console.log('🛒 [Context] Removing item from cart:', { cartItemId });

      const { error } = await supabase
        .from('cart_items')
        .delete()
        .eq('id', cartItemId);

      if (error) throw error;

      console.log('✅ [Context] Item removed, refreshing cart data...');
      await fetchCartItems();
      console.log('🔄 [Context] Cart data refreshed globally after item removal');
    } catch (err) {
      console.error('❌ [Context] Failed to remove item:', err);
      throw new Error(err instanceof Error ? err.message : 'Failed to remove item from cart');
    }
  }, [fetchCartItems]);

  const clearCart = useCallback(async () => {
    if (!user) return;

    try {
      console.log('🛒 [Context] Clearing entire cart');

      const { error } = await supabase
        .from('cart_items')
        .delete()
        .eq('user_id', user.id);

      if (error) throw error;

      console.log('✅ [Context] Cart cleared, refreshing data...');
      setCartItems([]);
      console.log('🔄 [Context] Cart data cleared globally');
    } catch (err) {
      console.error('❌ [Context] Failed to clear cart:', err);
      throw new Error(err instanceof Error ? err.message : 'Failed to clear cart');
    }
  }, [user]);

  const cartTotal = cartItems.reduce((total, item) => {
    return total + (item.products.price * item.quantity);
  }, 0);

  const cartItemCount = cartItems.reduce((count, item) => count + item.quantity, 0);

  const value: CartContextType = {
    cartItems,
    loading,
    error,
    cartTotal,
    cartItemCount,
    addToCart,
    updateQuantity,
    removeFromCart,
    clearCart,
    refetch: fetchCartItems
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
};

export const useCart = () => {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};