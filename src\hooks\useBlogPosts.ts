import { useState, useEffect, useCallback } from 'react';
import { supabase, type BlogPost } from '../lib/supabase';

export const useBlogPosts = () => {
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPublishedPosts = useCallback(async () => {
    try {
      setLoading(true);
      console.log('📝 Fetching published blog posts...');

      const { data, error } = await supabase
        .from('blog_posts')
        .select(`
          *,
          profiles:author_id (
            full_name,
            email
          )
        `)
        .eq('status', 'published')
        .not('published_at', 'is', null)
        .order('published_at', { ascending: false });

      if (error) throw error;

      console.log('✅ Published blog posts fetched:', data?.length || 0);
      setBlogPosts(data || []);
      setError(null);
    } catch (err) {
      console.error('❌ Failed to fetch published blog posts:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchAllPosts = useCallback(async () => {
    try {
      setLoading(true);
      console.log('📝 Fetching all blog posts (admin)...');

      const { data, error } = await supabase
        .from('blog_posts')
        .select(`
          *,
          profiles:author_id (
            full_name,
            email
          )
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;

      console.log('✅ All blog posts fetched:', data?.length || 0);
      setBlogPosts(data || []);
      setError(null);
    } catch (err) {
      console.error('❌ Failed to fetch all blog posts:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  }, []);

  const getPostBySlug = useCallback(async (slug: string): Promise<BlogPost | null> => {
    try {
      console.log('📝 Fetching blog post by slug:', slug);

      const { data, error } = await supabase
        .from('blog_posts')
        .select(`
          *,
          profiles:author_id (
            full_name,
            email
          )
        `)
        .eq('slug', slug)
        .eq('status', 'published')
        .not('published_at', 'is', null)
        .single();

      if (error) throw error;

      console.log('✅ Blog post fetched by slug');
      return data;
    } catch (err) {
      console.error('❌ Failed to fetch blog post by slug:', err);
      return null;
    }
  }, []);

  const createPost = useCallback(async (postData: {
    title: string;
    slug: string;
    excerpt?: string;
    content: string;
    featured_image?: string;
    tags?: string[];
    meta_title?: string;
    meta_description?: string;
    status?: 'draft' | 'published';
  }) => {
    try {
      console.log('📝 Creating new blog post...');

      const { data: userData } = await supabase.auth.getUser();
      if (!userData.user) throw new Error('User not authenticated');

      const postToInsert = {
        ...postData,
        author_id: userData.user.id,
        published_at: postData.status === 'published' ? new Date().toISOString() : null
      };

      const { data, error } = await supabase
        .from('blog_posts')
        .insert([postToInsert])
        .select()
        .single();

      if (error) throw error;

      console.log('✅ Blog post created successfully');
      return data;
    } catch (err) {
      console.error('❌ Failed to create blog post:', err);
      throw err;
    }
  }, []);

  const updatePost = useCallback(async (id: string, postData: {
    title?: string;
    slug?: string;
    excerpt?: string;
    content?: string;
    featured_image?: string;
    tags?: string[];
    meta_title?: string;
    meta_description?: string;
    status?: 'draft' | 'published' | 'archived';
  }) => {
    try {
      console.log('📝 Updating blog post:', id);

      const updateData = { ...postData };
      
      // If publishing a draft, set published_at
      if (postData.status === 'published') {
        updateData.published_at = new Date().toISOString();
      }
      // If unpublishing, clear published_at
      else if (postData.status === 'draft' || postData.status === 'archived') {
        updateData.published_at = null;
      }

      const { data, error } = await supabase
        .from('blog_posts')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      console.log('✅ Blog post updated successfully');
      return data;
    } catch (err) {
      console.error('❌ Failed to update blog post:', err);
      throw err;
    }
  }, []);

  const deletePost = useCallback(async (id: string) => {
    try {
      console.log('📝 Deleting blog post:', id);

      const { error } = await supabase
        .from('blog_posts')
        .delete()
        .eq('id', id);

      if (error) throw error;

      console.log('✅ Blog post deleted successfully');
    } catch (err) {
      console.error('❌ Failed to delete blog post:', err);
      throw err;
    }
  }, []);

  const generateSlug = useCallback((title: string): string => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9 -]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }, []);

  useEffect(() => {
    fetchPublishedPosts();
  }, [fetchPublishedPosts]);

  return {
    blogPosts,
    loading,
    error,
    fetchPublishedPosts,
    fetchAllPosts,
    getPostBySlug,
    createPost,
    updatePost,
    deletePost,
    generateSlug,
    refetch: fetchPublishedPosts
  };
};
