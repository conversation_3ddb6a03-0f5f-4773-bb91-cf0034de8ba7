import { useState, useEffect, useCallback } from 'react';
import { supabase, type Product } from '../lib/supabase';
import { getProductsSafely, getProductByIdSafely } from '../utils/tempDatabaseFix';

export const useProducts = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchProducts = useCallback(async () => {
    try {
      setLoading(true);
      console.log('🛍️ Starting product fetch...');
      const startTime = performance.now();

      const data = await getProductsSafely();

      const endTime = performance.now();
      const fetchTime = (endTime - startTime).toFixed(2);

      console.log('✅ Products fetched successfully:', {
        count: data?.length || 0,
        fetchTime: `${fetchTime}ms`,
        usingFallback: !data || data.length === 0
      });

      setProducts(data || []);
      setError(''); // Clear any previous errors
    } catch (err) {
      console.error('❌ Product fetch failed:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchProducts();
  }, [fetchProducts]);

  const getProductById = useCallback(async (id: string): Promise<Product | null> => {
    try {
      console.log('🛍️ Fetching product by ID with safe fallback...');
      const data = await getProductByIdSafely(id);
      return data;
    } catch (err) {
      console.error('Error fetching product:', err);
      return null;
    }
  }, []);

  const searchProducts = useCallback(async (query: string): Promise<Product[]> => {
    try {
      const { data, error } = await supabase
        .from('products')
        .select(`
          *,
          categories (
            name,
            slug
          )
        `)
        .or(`name.ilike.%${query}%, description.ilike.%${query}%, brand.ilike.%${query}%`)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (err) {
      console.error('Error searching products:', err);
      return [];
    }
  }, []);

  const getProductsByCategory = useCallback(async (categorySlug: string): Promise<Product[]> => {
    try {
      const { data, error } = await supabase
        .from('products')
        .select(`
          *,
          categories!inner (
            name,
            slug
          )
        `)
        .eq('categories.slug', categorySlug)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (err) {
      console.error('Error fetching products by category:', err);
      return [];
    }
  }, []);

  return {
    products,
    loading,
    error,
    refetch: fetchProducts,
    getProductById,
    searchProducts,
    getProductsByCategory
  };
};