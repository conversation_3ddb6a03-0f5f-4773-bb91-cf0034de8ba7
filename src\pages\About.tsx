import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Award, Users, Globe, Heart, Sparkles, ArrowRight, Shield, Zap, Star } from 'lucide-react';

const About: React.FC = () => {
  const values = [
    {
      icon: Award,
      title: 'Excellence',
      description: 'We pursue perfection in every stitch, every detail, and every customer interaction.'
    },
    {
      icon: Heart,
      title: 'Passion',
      description: 'Our love for fashion drives us to create pieces that inspire and empower.'
    },
    {
      icon: Shield,
      title: 'Integrity',
      description: 'Honest craftsmanship and transparent business practices define who we are.'
    },
    {
      icon: Globe,
      title: 'Sustainability',
      description: 'We are committed to ethical practices and environmental responsibility.'
    }
  ];

  const team = [
    {
      name: '<PERSON>',
      role: 'Founder & Creative Director',
      image: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=400',
      bio: 'With 15+ years in luxury fashion, <PERSON> founded LUXE to bridge timeless elegance with modern innovation.'
    },
    {
      name: '<PERSON>',
      role: 'Head of Design',
      image: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=400',
      bio: 'Former Parisian couturier, <PERSON> brings European sophistication to every collection.'
    },
    {
      name: 'Sophia Williams',
      role: 'Sustainability Director',
      image: 'https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=400',
      bio: 'Leading our commitment to ethical fashion and environmental responsibility.'
    }
  ];

  const milestones = [
    { year: '2020', event: 'LUXE Founded', description: 'Started with a vision to redefine luxury fashion' },
    { year: '2021', event: 'First Collection', description: 'Launched our signature evening wear line' },
    { year: '2022', event: 'Global Expansion', description: 'Expanded to 25 countries worldwide' },
    { year: '2023', event: 'Sustainability Initiative', description: 'Achieved carbon-neutral operations' },
    { year: '2024', event: 'Master Artisan Program', description: 'Partnered with 200+ skilled craftspeople' },
    { year: '2025', event: 'Innovation Lab', description: 'Opened our design and innovation center' }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative py-32 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M50 0L60.876 39.124L100 50L60.876 60.876L50 100L39.124 60.876L0 50L39.124 39.124z' fill='%23ffffff' fill-opacity='0.1'/%3E%3C/svg%3E")`,
            backgroundRepeat: 'repeat'
          }} />
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center">
            <div className="inline-flex items-center space-x-2 bg-white/10 rounded-full px-6 py-2 mb-8 backdrop-blur-sm">
              <Sparkles className="h-4 w-4 text-amber-400" />
              <span className="text-amber-300 text-sm font-medium uppercase tracking-wider">Our Story</span>
            </div>
            
            <h1 className="text-5xl md:text-7xl font-light mb-8 leading-tight">
              Crafting
              <span className="block font-normal bg-gradient-to-r from-amber-300 to-amber-500 bg-clip-text text-transparent">
                Luxury
              </span>
              <span className="block font-light">Since 2020</span>
            </h1>
            
            <p className="text-xl md:text-2xl text-slate-300 max-w-4xl mx-auto leading-relaxed">
              Born from a passion for exceptional craftsmanship and timeless design, 
              LUXE represents the perfect harmony between tradition and innovation.
            </p>
          </div>
        </div>
      </section>

      {/* Mission Statement */}
      <section className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <div className="inline-flex items-center space-x-2 bg-slate-50 rounded-full px-6 py-2 mb-6">
                <Heart className="h-4 w-4 text-slate-600" />
                <span className="text-slate-600 text-sm font-medium uppercase tracking-wider">Our Mission</span>
              </div>
              
              <h2 className="text-4xl md:text-5xl font-light text-slate-900 mb-8">
                Redefining
                <span className="font-normal"> Excellence</span>
              </h2>
              
              <p className="text-xl text-slate-600 mb-8 leading-relaxed">
                At LUXE, we believe that true luxury lies not just in the finest materials or 
                impeccable craftsmanship, but in the story each piece tells and the confidence 
                it inspires in those who wear it.
              </p>
              
              <p className="text-lg text-slate-600 mb-8 leading-relaxed">
                Every garment in our collection is a testament to our unwavering commitment to 
                quality, sustainability, and the artistry that transforms premium materials into 
                timeless treasures that transcend seasons and trends.
              </p>

              <Link
                to="/products"
                className="inline-flex items-center space-x-3 bg-gradient-to-r from-amber-500 to-amber-600 text-white px-8 py-4 rounded-full font-medium hover:from-amber-600 hover:to-amber-700 transition-all duration-300 transform hover:scale-105"
              >
                <span>Explore Our Collection</span>
                <ArrowRight className="h-5 w-5" />
              </Link>
            </div>

            <div className="relative">
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-6">
                  <img
                    src="https://images.pexels.com/photos/1926769/pexels-photo-1926769.jpeg?auto=compress&cs=tinysrgb&w=500"
                    alt="Craftsmanship"
                    className="w-full h-64 object-cover rounded-2xl shadow-lg"
                  />
                  <img
                    src="https://images.pexels.com/photos/1884581/pexels-photo-1884581.jpeg?auto=compress&cs=tinysrgb&w=500"
                    alt="Design Process"
                    className="w-full h-48 object-cover rounded-2xl shadow-lg"
                  />
                </div>
                <div className="space-y-6 mt-12">
                  <img
                    src="https://images.pexels.com/photos/1536619/pexels-photo-1536619.jpeg?auto=compress&cs=tinysrgb&w=500"
                    alt="Fashion Excellence"
                    className="w-full h-48 object-cover rounded-2xl shadow-lg"
                  />
                  <img
                    src="https://images.pexels.com/photos/1697214/pexels-photo-1697214.jpeg?auto=compress&cs=tinysrgb&w=500"
                    alt="Luxury Details"
                    className="w-full h-64 object-cover rounded-2xl shadow-lg"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-24 bg-slate-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-light text-slate-900 mb-6">
              Our
              <span className="font-normal"> Values</span>
            </h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">
              The principles that guide everything we do
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <div key={index} className="group text-center">
                <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-slate-100 to-slate-200 rounded-2xl flex items-center justify-center group-hover:from-amber-50 group-hover:to-amber-100 transition-all duration-300">
                  <value.icon className="h-10 w-10 text-slate-600 group-hover:text-amber-600 transition-colors duration-300" />
                </div>
                <h3 className="text-2xl font-light text-slate-900 mb-4">{value.title}</h3>
                <p className="text-slate-600 leading-relaxed">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="inline-flex items-center space-x-2 bg-slate-50 rounded-full px-6 py-2 mb-6">
              <Users className="h-4 w-4 text-slate-600" />
              <span className="text-slate-600 text-sm font-medium uppercase tracking-wider">Meet Our Team</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-light text-slate-900 mb-6">
              Visionary
              <span className="font-normal"> Leaders</span>
            </h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">
              The passionate individuals behind LUXE's success
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {team.map((member, index) => (
              <div key={index} className="group text-center">
                <div className="relative mb-6">
                  <div className="w-48 h-48 mx-auto rounded-full overflow-hidden shadow-lg group-hover:shadow-2xl transition-shadow duration-300">
                    <img
                      src={member.image}
                      alt={member.name}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                  </div>
                  <div className="absolute inset-0 rounded-full bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </div>
                <h3 className="text-2xl font-light text-slate-900 mb-2">{member.name}</h3>
                <p className="text-amber-600 font-medium mb-4 uppercase tracking-wider text-sm">{member.role}</p>
                <p className="text-slate-600 leading-relaxed">{member.bio}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Timeline Section */}
      <section className="py-24 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="inline-flex items-center space-x-2 bg-white/10 rounded-full px-6 py-2 mb-6 backdrop-blur-sm">
              <Star className="h-4 w-4 text-amber-400" />
              <span className="text-amber-300 text-sm font-medium uppercase tracking-wider">Our Journey</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-light mb-6">
              Milestones of
              <span className="font-normal bg-gradient-to-r from-amber-300 to-amber-500 bg-clip-text text-transparent"> Excellence</span>
            </h2>
            <p className="text-xl text-slate-300 max-w-2xl mx-auto">
              Key moments that shaped our luxury fashion journey
            </p>
          </div>

          <div className="relative">
            {/* Timeline Line */}
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-amber-400 to-amber-600 rounded-full" />

            <div className="space-y-12">
              {milestones.map((milestone, index) => (
                <div key={index} className={`flex items-center ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}>
                  <div className={`w-1/2 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                    <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 hover:bg-white/15 transition-colors duration-300">
                      <div className="text-2xl font-light text-amber-400 mb-2">{milestone.year}</div>
                      <h3 className="text-xl font-medium text-white mb-3">{milestone.event}</h3>
                      <p className="text-slate-300">{milestone.description}</p>
                    </div>
                  </div>

                  {/* Timeline Dot */}
                  <div className="relative z-10">
                    <div className="w-6 h-6 bg-gradient-to-r from-amber-400 to-amber-600 rounded-full border-4 border-slate-900" />
                  </div>

                  <div className="w-1/2" />
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-4xl md:text-5xl font-light text-slate-900 mb-2">50K+</div>
              <div className="text-slate-600 uppercase tracking-wider text-sm">Satisfied Clients</div>
            </div>
            <div className="text-center">
              <div className="text-4xl md:text-5xl font-light text-slate-900 mb-2">200+</div>
              <div className="text-slate-600 uppercase tracking-wider text-sm">Master Artisans</div>
            </div>
            <div className="text-center">
              <div className="text-4xl md:text-5xl font-light text-slate-900 mb-2">50+</div>
              <div className="text-slate-600 uppercase tracking-wider text-sm">Countries Served</div>
            </div>
            <div className="text-center">
              <div className="text-4xl md:text-5xl font-light text-slate-900 mb-2">99%</div>
              <div className="text-slate-600 uppercase tracking-wider text-sm">Quality Rating</div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-slate-50">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-4xl md:text-5xl font-light text-slate-900 mb-6">
            Experience
            <span className="font-normal"> LUXE</span>
          </h2>
          <p className="text-xl text-slate-600 mb-12 max-w-2xl mx-auto">
            Discover the difference that true craftsmanship and attention to detail can make
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Link
              to="/products"
              className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white px-12 py-4 rounded-full text-lg font-medium transition-all duration-300 transform hover:scale-105"
            >
              Shop Collection
            </Link>
            <Link
              to="/blog"
              className="border-2 border-slate-900 text-slate-900 hover:bg-slate-900 hover:text-white px-12 py-4 rounded-full text-lg font-medium transition-all duration-300"
            >
              Read Our Blog
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default About;
