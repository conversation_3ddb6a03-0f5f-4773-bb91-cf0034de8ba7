import React from 'react';
import { Navigate, Routes, Route, Link, useLocation } from 'react-router-dom';
import { Package, Users, BarChart3, Settings, Edit } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import AdminDashboard from '../components/admin/AdminDashboard';
import ProductManagement from '../components/admin/ProductManagement';
import BlogManagement from '../components/admin/BlogManagement';

const Admin: React.FC = () => {
  const { user, isAdmin, loading: authLoading } = useAuth();
  const location = useLocation();

  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-500"></div>
      </div>
    );
  }

  if (!user || !isAdmin) {
    return <Navigate to="/login" replace />;
  }

  const isActive = (path: string) => {
    return location.pathname === path || (path === '/admin/dashboard' && location.pathname === '/admin');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex">
        {/* Sidebar */}
        <div className="w-64 bg-white shadow-sm min-h-screen">
          <div className="p-6">
            <h1 className="text-xl font-bold text-slate-800">Admin Panel</h1>
            <p className="text-sm text-gray-600 mt-1">Manage your store</p>
          </div>

          <nav className="mt-6">
            <Link
              to="/admin/dashboard"
              className={`flex items-center px-6 py-3 text-sm font-medium transition-colors ${
                isActive('/admin/dashboard')
                  ? 'bg-amber-50 text-amber-700 border-r-2 border-amber-500'
                  : 'text-gray-700 hover:bg-gray-50'
              }`}
            >
              <BarChart3 className="h-5 w-5 mr-3" />
              Dashboard
            </Link>

            <Link
              to="/admin/product-management"
              className={`flex items-center px-6 py-3 text-sm font-medium transition-colors ${
                isActive('/admin/product-management')
                  ? 'bg-amber-50 text-amber-700 border-r-2 border-amber-500'
                  : 'text-gray-700 hover:bg-gray-50'
              }`}
            >
              <Package className="h-5 w-5 mr-3" />
              Product Management
            </Link>

            <Link
              to="/admin/blog-management"
              className={`flex items-center px-6 py-3 text-sm font-medium transition-colors ${
                isActive('/admin/blog-management')
                  ? 'bg-amber-50 text-amber-700 border-r-2 border-amber-500'
                  : 'text-gray-700 hover:bg-gray-50'
              }`}
            >
              <Edit className="h-5 w-5 mr-3" />
              Blog Management
            </Link>

            <Link
              to="/admin/users"
              className={`flex items-center px-6 py-3 text-sm font-medium transition-colors ${
                isActive('/admin/users')
                  ? 'bg-amber-50 text-amber-700 border-r-2 border-amber-500'
                  : 'text-gray-700 hover:bg-gray-50'
              }`}
            >
              <Users className="h-5 w-5 mr-3" />
              User Management
            </Link>

            <Link
              to="/admin/settings"
              className={`flex items-center px-6 py-3 text-sm font-medium transition-colors ${
                isActive('/admin/settings')
                  ? 'bg-amber-50 text-amber-700 border-r-2 border-amber-500'
                  : 'text-gray-700 hover:bg-gray-50'
              }`}
            >
              <Settings className="h-5 w-5 mr-3" />
              Settings
            </Link>
          </nav>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-8">
          <Routes>
            <Route path="/" element={<Navigate to="/admin/dashboard" replace />} />
            <Route path="/dashboard" element={<AdminDashboard />} />
            <Route path="/product-management" element={<ProductManagement />} />
            <Route path="/blog-management" element={<BlogManagement />} />
            <Route path="/users" element={<UserManagementPlaceholder />} />
            <Route path="/settings" element={<SettingsPlaceholder />} />
          </Routes>
        </div>
      </div>
    </div>
  );
};

// Placeholder components for future implementation
const UserManagementPlaceholder: React.FC = () => (
  <div className="text-center py-12">
    <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
    <h3 className="text-lg font-semibold text-slate-800 mb-2">User Management</h3>
    <p className="text-gray-600">User management functionality coming soon</p>
  </div>
);

const SettingsPlaceholder: React.FC = () => (
  <div className="text-center py-12">
    <Settings className="h-12 w-12 text-gray-400 mx-auto mb-4" />
    <h3 className="text-lg font-semibold text-slate-800 mb-2">Settings</h3>
    <p className="text-gray-600">Settings panel coming soon</p>
  </div>
);

export default Admin;