import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { Calendar, User, Tag, ArrowLeft, Share2, Clock } from 'lucide-react';
import { useBlogPosts } from '../hooks/useBlogPosts';
import { BlogPost as BlogPostType } from '../lib/supabase';

const BlogPost: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();
  const { getPostBySlug } = useBlogPosts();
  const [post, setPost] = useState<BlogPostType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPost = async () => {
      if (!slug) {
        setError('No post slug provided');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const postData = await getPostBySlug(slug);
        
        if (!postData) {
          setError('Post not found');
        } else {
          setPost(postData);
        }
      } catch (err) {
        setError('Failed to load post');
        console.error('Error fetching blog post:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchPost();
  }, [slug, getPostBySlug]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getReadingTime = (content: string) => {
    const wordsPerMinute = 200;
    const wordCount = content.split(/\s+/).length;
    const readingTime = Math.ceil(wordCount / wordsPerMinute);
    return `${readingTime} min read`;
  };

  const handleShare = async () => {
    if (navigator.share && post) {
      try {
        await navigator.share({
          title: post.title,
          text: post.excerpt || '',
          url: window.location.href,
        });
      } catch (err) {
        console.log('Error sharing:', err);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      // You could show a toast notification here
    }
  };

  const formatContent = (content: string) => {
    // Enhanced markdown parsing
    const lines = content.split('\n');
    const elements: JSX.Element[] = [];
    let currentParagraph: string[] = [];
    let listItems: string[] = [];
    let inCodeBlock = false;
    let codeBlockContent: string[] = [];
    let elementIndex = 0;

    const flushParagraph = () => {
      if (currentParagraph.length > 0) {
        const paragraphText = currentParagraph.join(' ').trim();
        if (paragraphText) {
          elements.push(
            <p key={elementIndex++} className="text-lg text-slate-700 leading-relaxed mb-6">
              {formatInlineElements(paragraphText)}
            </p>
          );
        }
        currentParagraph = [];
      }
    };

    const flushList = () => {
      if (listItems.length > 0) {
        elements.push(
          <ul key={elementIndex++} className="list-disc list-inside mb-6 space-y-2">
            {listItems.map((item, idx) => (
              <li key={idx} className="text-lg text-slate-700 leading-relaxed ml-4">
                {formatInlineElements(item)}
              </li>
            ))}
          </ul>
        );
        listItems = [];
      }
    };

    const flushCodeBlock = () => {
      if (codeBlockContent.length > 0) {
        elements.push(
          <pre key={elementIndex++} className="bg-slate-100 rounded-lg p-4 mb-6 overflow-x-auto">
            <code className="text-sm text-slate-800">
              {codeBlockContent.join('\n')}
            </code>
          </pre>
        );
        codeBlockContent = [];
      }
    };

    const formatInlineElements = (text: string) => {
      // Handle bold text **text**
      text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
      // Handle italic text *text*
      text = text.replace(/\*(.*?)\*/g, '<em>$1</em>');
      // Handle underlined text __text__
      text = text.replace(/__(.*?)__/g, '<u class="underline">$1</u>');
      // Handle inline code `code`
      text = text.replace(/`(.*?)`/g, '<code class="bg-slate-100 px-2 py-1 rounded text-sm">$1</code>');
      // Handle links [text](url)
      text = text.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-amber-600 hover:text-amber-700 underline" target="_blank" rel="noopener noreferrer">$1</a>');
      // Handle HTML color spans (from rich text editor)
      text = text.replace(/<span style="color: ([^"]+)">(.*?)<\/span>/g, '<span style="color: $1">$2</span>');
      // Handle images ![alt](url) - consistent with admin editor
      text = text.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, '<img src="$2" alt="$1" class="max-w-full h-auto rounded-lg my-4 shadow-sm" />');

      return <span dangerouslySetInnerHTML={{ __html: text }} />;
    };

    lines.forEach((line) => {
      const trimmedLine = line.trim();

      // Handle code blocks
      if (trimmedLine.startsWith('```')) {
        if (inCodeBlock) {
          flushCodeBlock();
          inCodeBlock = false;
        } else {
          flushParagraph();
          flushList();
          inCodeBlock = true;
        }
        return;
      }

      if (inCodeBlock) {
        codeBlockContent.push(line);
        return;
      }

      // Handle headings
      if (trimmedLine.startsWith('# ')) {
        flushParagraph();
        flushList();
        elements.push(
          <h1 key={elementIndex++} className="text-4xl font-bold text-slate-900 mt-12 mb-6">
            {trimmedLine.replace('# ', '')}
          </h1>
        );
        return;
      }

      if (trimmedLine.startsWith('## ')) {
        flushParagraph();
        flushList();
        elements.push(
          <h2 key={elementIndex++} className="text-3xl font-semibold text-slate-900 mt-10 mb-5">
            {trimmedLine.replace('## ', '')}
          </h2>
        );
        return;
      }

      if (trimmedLine.startsWith('### ')) {
        flushParagraph();
        flushList();
        elements.push(
          <h3 key={elementIndex++} className="text-2xl font-semibold text-slate-900 mt-8 mb-4">
            {trimmedLine.replace('### ', '')}
          </h3>
        );
        return;
      }

      if (trimmedLine.startsWith('#### ')) {
        flushParagraph();
        flushList();
        elements.push(
          <h4 key={elementIndex++} className="text-xl font-semibold text-slate-900 mt-6 mb-3">
            {trimmedLine.replace('#### ', '')}
          </h4>
        );
        return;
      }

      // Handle blockquotes
      if (trimmedLine.startsWith('> ')) {
        flushParagraph();
        flushList();
        elements.push(
          <blockquote key={elementIndex++} className="border-l-4 border-amber-500 pl-6 py-2 mb-6 italic text-slate-600 bg-slate-50 rounded-r-lg">
            {formatInlineElements(trimmedLine.replace('> ', ''))}
          </blockquote>
        );
        return;
      }

      // Handle list items
      if (trimmedLine.startsWith('- ') || trimmedLine.startsWith('* ')) {
        flushParagraph();
        listItems.push(trimmedLine.replace(/^[*-] /, ''));
        return;
      }

      // Handle horizontal rules
      if (trimmedLine === '---' || trimmedLine === '***') {
        flushParagraph();
        flushList();
        elements.push(
          <hr key={elementIndex++} className="border-slate-200 my-8" />
        );
        return;
      }

      // Handle empty lines
      if (trimmedLine === '') {
        flushParagraph();
        flushList();
        return;
      }

      // Regular paragraph content
      currentParagraph.push(line);
    });

    // Flush any remaining content
    flushParagraph();
    flushList();
    flushCodeBlock();

    return elements;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-500 mx-auto mb-4"></div>
          <p className="text-slate-600">Loading article...</p>
        </div>
      </div>
    );
  }

  if (error || !post) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-slate-900 mb-4">Article Not Found</h1>
          <p className="text-slate-600 mb-6">{error || 'The requested article could not be found.'}</p>
          <div className="space-x-4">
            <button
              onClick={() => navigate(-1)}
              className="bg-slate-800 hover:bg-slate-900 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Go Back
            </button>
            <Link
              to="/blog"
              className="bg-amber-500 hover:bg-amber-600 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              View All Articles
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative py-24 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white">
        {post.featured_image && (
          <div className="absolute inset-0">
            <img
              src={post.featured_image}
              alt={post.title}
              className="w-full h-full object-cover opacity-20"
            />
            <div className="absolute inset-0 bg-gradient-to-br from-slate-900/90 via-slate-800/90 to-slate-900/90" />
          </div>
        )}
        
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          {/* Back Button */}
          <Link
            to="/blog"
            className="inline-flex items-center space-x-2 text-amber-300 hover:text-amber-200 transition-colors mb-8"
          >
            <ArrowLeft className="h-5 w-5" />
            <span>Back to Blog</span>
          </Link>

          {/* Tags */}
          {post.tags && post.tags.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-6">
              {post.tags.map(tag => (
                <span
                  key={tag}
                  className="inline-flex items-center space-x-1 bg-amber-500/20 text-amber-300 px-3 py-1 rounded-full text-sm font-medium"
                >
                  <Tag className="h-3 w-3" />
                  <span className="capitalize">{tag}</span>
                </span>
              ))}
            </div>
          )}

          {/* Title */}
          <h1 className="text-4xl md:text-5xl font-light mb-6 leading-tight">
            {post.title}
          </h1>

          {/* Excerpt */}
          {post.excerpt && (
            <p className="text-xl text-slate-300 mb-8 leading-relaxed">
              {post.excerpt}
            </p>
          )}

          {/* Meta Info */}
          <div className="flex flex-wrap items-center gap-6 text-slate-300">
            <div className="flex items-center space-x-2">
              <Calendar className="h-5 w-5" />
              <span>{formatDate(post.published_at || post.created_at)}</span>
            </div>
            <div className="flex items-center space-x-2">
              <User className="h-5 w-5" />
              <span>{(post as any).profiles?.full_name || 'LUXE Team'}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5" />
              <span>{getReadingTime(post.content)}</span>
            </div>
            <button
              onClick={handleShare}
              className="flex items-center space-x-2 text-amber-300 hover:text-amber-200 transition-colors"
            >
              <Share2 className="h-5 w-5" />
              <span>Share</span>
            </button>
          </div>
        </div>
      </section>

      {/* Article Content */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="prose prose-lg max-w-none">
            {formatContent(post.content)}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-slate-50">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-light text-slate-900 mb-6">
            Discover LUXE
          </h2>
          <p className="text-xl text-slate-600 mb-8">
            Experience the luxury and craftsmanship that inspired this article
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/products"
              className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white px-8 py-3 rounded-full font-medium transition-all duration-300 transform hover:scale-105"
            >
              Shop Collection
            </Link>
            <Link
              to="/blog"
              className="border-2 border-slate-900 text-slate-900 hover:bg-slate-900 hover:text-white px-8 py-3 rounded-full font-medium transition-all duration-300"
            >
              More Articles
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default BlogPost;
