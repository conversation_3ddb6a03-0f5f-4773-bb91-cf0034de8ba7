import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { ChevronLeft, ChevronRight, Star, ArrowRight, Check, Play, Sparkles, Award, Shield, Zap } from 'lucide-react';
import { useProducts } from '../hooks/useProducts';

const Home: React.FC = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [email, setEmail] = useState('');
  const [subscribed, setSubscribed] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const { products } = useProducts();

  useEffect(() => {
    setIsVisible(true);

    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const featuredProducts = products.slice(0, 6);
  const testimonials = [
    {
      id: 1,
      name: '<PERSON>',
      text: 'Absolutely love the quality and style. Every piece I\'ve purchased has exceeded my expectations.',
      rating: 5,
      image: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=200'
    },
    {
      id: 2,
      name: 'Michael Chen',
      text: 'The attention to detail is remarkable. These pieces are worth every penny.',
      rating: 5,
      image: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=200'
    },
    {
      id: 3,
      name: 'Emma Rodriguez',
      text: 'Fast shipping, beautiful packaging, and incredible quality. My new favorite store!',
      rating: 5,
      image: 'https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?auto=compress&cs=tinysrgb&w=200'
    }
  ];

  const handleNewsletterSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (email) {
      setSubscribed(true);
      setEmail('');
      setTimeout(() => setSubscribed(false), 3000);
    }
  };

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % featuredProducts.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + featuredProducts.length) % featuredProducts.length);
  };

  return (
    <div className="overflow-hidden">
      {/* Premium Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Hero Background Images */}
        <div className="absolute inset-0">
          {/* Main Background Image */}
          <div className="absolute inset-0">
            <img
              src="https://images.pexels.com/photos/1926769/pexels-photo-1926769.jpeg?auto=compress&cs=tinysrgb&w=1920"
              alt="Luxury Fashion"
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-br from-slate-950/90 via-slate-900/85 to-slate-800/90" />
          </div>

          {/* Floating Product Images */}
          <div className="absolute inset-0 pointer-events-none">
            {/* Top Left - Elegant Watch */}
            <div className="absolute top-20 left-20 w-32 h-32 opacity-20 animate-pulse">
              <img
                src="https://images.pexels.com/photos/1697214/pexels-photo-1697214.jpeg?auto=compress&cs=tinysrgb&w=300"
                alt="Luxury Watch"
                className="w-full h-full object-cover rounded-2xl shadow-2xl"
              />
              <div className="absolute inset-0 bg-gradient-to-br from-amber-400/30 to-transparent rounded-2xl" />
            </div>

            {/* Top Right - Designer Handbag */}
            <div className="absolute top-32 right-16 w-40 h-28 opacity-15 animate-pulse" style={{ animationDelay: '1s' }}>
              <img
                src="https://images.pexels.com/photos/1884581/pexels-photo-1884581.jpeg?auto=compress&cs=tinysrgb&w=400"
                alt="Designer Handbag"
                className="w-full h-full object-cover rounded-2xl shadow-2xl"
              />
              <div className="absolute inset-0 bg-gradient-to-br from-slate-400/20 to-transparent rounded-2xl" />
            </div>

            {/* Bottom Left - Luxury Jewelry */}
            <div className="absolute bottom-40 left-32 w-24 h-24 opacity-25 animate-pulse" style={{ animationDelay: '2s' }}>
              <img
                src="https://images.pexels.com/photos/1927259/pexels-photo-1927259.jpeg?auto=compress&cs=tinysrgb&w=300"
                alt="Luxury Jewelry"
                className="w-full h-full object-cover rounded-full shadow-2xl"
              />
              <div className="absolute inset-0 bg-gradient-to-br from-amber-300/40 to-transparent rounded-full" />
            </div>

            {/* Bottom Right - Premium Shoes */}
            <div className="absolute bottom-24 right-24 w-36 h-24 opacity-20 animate-pulse" style={{ animationDelay: '0.5s' }}>
              <img
                src="https://images.pexels.com/photos/1464625/pexels-photo-1464625.jpeg?auto=compress&cs=tinysrgb&w=400"
                alt="Premium Shoes"
                className="w-full h-full object-cover rounded-2xl shadow-2xl"
              />
              <div className="absolute inset-0 bg-gradient-to-br from-slate-300/25 to-transparent rounded-2xl" />
            </div>

            {/* Center Right - Fashion Model Silhouette */}
            <div className="absolute top-1/2 right-8 w-20 h-32 opacity-10 animate-pulse" style={{ animationDelay: '1.5s' }}>
              <img
                src="https://images.pexels.com/photos/1536619/pexels-photo-1536619.jpeg?auto=compress&cs=tinysrgb&w=300"
                alt="Fashion Elegance"
                className="w-full h-full object-cover rounded-3xl shadow-2xl"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-amber-500/30 to-transparent rounded-3xl" />
            </div>
          </div>
        </div>

        {/* Interactive Mouse Effect */}
        <div
          className="absolute inset-0 opacity-20 pointer-events-none"
          style={{
            background: `radial-gradient(circle at ${mousePosition.x}px ${mousePosition.y}px, rgba(255,255,255,0.1) 0%, transparent 50%)`
          }}
        />

        {/* Subtle Pattern Overlay */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent" />
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            backgroundRepeat: 'repeat'
          }} />
        </div>

        {/* Floating Light Elements */}
        <div className="absolute inset-0 pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-gradient-to-r from-amber-400 to-amber-600 rounded-full animate-pulse opacity-60" />
          <div className="absolute top-3/4 right-1/3 w-1 h-1 bg-gradient-to-r from-rose-400 to-rose-600 rounded-full animate-pulse opacity-40" />
          <div className="absolute top-1/2 right-1/4 w-1.5 h-1.5 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full animate-pulse opacity-50" />
          <div className="absolute top-1/3 right-1/2 w-1 h-1 bg-gradient-to-r from-amber-300 to-amber-500 rounded-full animate-pulse opacity-50" style={{ animationDelay: '1s' }} />
        </div>

        <div className={`relative z-10 text-center max-w-6xl mx-auto px-4 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
          {/* Premium Badge */}
          <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-amber-500/10 to-amber-600/10 border border-amber-500/20 rounded-full px-6 py-2 mb-8 backdrop-blur-sm">
            <Sparkles className="h-4 w-4 text-amber-400" />
            <span className="text-amber-300 text-sm font-medium tracking-wide">LUXURY REDEFINED</span>
          </div>

          {/* Main Headline */}
          <h1 className="text-6xl md:text-8xl lg:text-9xl font-thin mb-8 leading-none tracking-tight">
            <span className="block text-white font-extralight">PURE</span>
            <span className="block bg-gradient-to-r from-amber-300 via-amber-400 to-amber-500 bg-clip-text text-transparent font-light">LUXURY</span>
          </h1>

          {/* Subtitle */}
          <p className="text-xl md:text-2xl lg:text-3xl mb-12 text-slate-300 max-w-4xl mx-auto font-light leading-relaxed">
            Where exceptional craftsmanship meets
            <span className="text-white font-normal"> timeless sophistication</span>
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
            <Link
              to="/products"
              className="group relative bg-gradient-to-r from-amber-500 to-amber-600 text-white px-12 py-4 rounded-full text-lg font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-2xl hover:shadow-amber-500/25 overflow-hidden"
            >
              <span className="relative z-10 flex items-center space-x-2">
                <span>Explore Collection</span>
                <ArrowRight className="h-5 w-5 transition-transform group-hover:translate-x-1" />
              </span>
              <div className="absolute inset-0 bg-gradient-to-r from-amber-600 to-amber-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            </Link>

            <button className="group flex items-center space-x-3 text-white hover:text-amber-300 transition-colors duration-300">
              <div className="w-12 h-12 rounded-full border-2 border-white/30 flex items-center justify-center group-hover:border-amber-300 transition-colors duration-300">
                <Play className="h-5 w-5 ml-0.5" />
              </div>
              <span className="text-lg font-light">Watch Our Story</span>
            </button>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-3 gap-8 max-w-2xl mx-auto">
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-light text-white mb-2">50K+</div>
              <div className="text-slate-400 text-sm uppercase tracking-wider">Satisfied Clients</div>
            </div>
            <div className="text-center border-x border-slate-700">
              <div className="text-3xl md:text-4xl font-light text-white mb-2">15+</div>
              <div className="text-slate-400 text-sm uppercase tracking-wider">Years Excellence</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-light text-white mb-2">99%</div>
              <div className="text-slate-400 text-sm uppercase tracking-wider">Quality Rating</div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse" />
          </div>
        </div>
      </section>

      {/* Premium Features Section */}
      <section className="py-24 bg-white relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Section Header */}
          <div className="text-center mb-20">
            <div className="inline-flex items-center space-x-2 bg-slate-50 rounded-full px-6 py-2 mb-6">
              <Award className="h-4 w-4 text-slate-600" />
              <span className="text-slate-600 text-sm font-medium uppercase tracking-wider">Why Choose Luxe</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-light text-slate-900 mb-6">
              Uncompromising
              <span className="font-normal"> Excellence</span>
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
              Every detail meticulously crafted to deliver an experience that transcends ordinary luxury
            </p>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
            <div className="group text-center">
              <div className="w-20 h-20 mx-auto mb-8 bg-gradient-to-br from-slate-100 to-slate-200 rounded-2xl flex items-center justify-center group-hover:from-amber-50 group-hover:to-amber-100 transition-all duration-300">
                <Shield className="h-10 w-10 text-slate-600 group-hover:text-amber-600 transition-colors duration-300" />
              </div>
              <h3 className="text-2xl font-light text-slate-900 mb-4">Lifetime Guarantee</h3>
              <p className="text-slate-600 leading-relaxed">
                Every piece comes with our comprehensive lifetime warranty, ensuring your investment is protected forever.
              </p>
            </div>

            <div className="group text-center">
              <div className="w-20 h-20 mx-auto mb-8 bg-gradient-to-br from-slate-100 to-slate-200 rounded-2xl flex items-center justify-center group-hover:from-amber-50 group-hover:to-amber-100 transition-all duration-300">
                <Zap className="h-10 w-10 text-slate-600 group-hover:text-amber-600 transition-colors duration-300" />
              </div>
              <h3 className="text-2xl font-light text-slate-900 mb-4">Express Delivery</h3>
              <p className="text-slate-600 leading-relaxed">
                White-glove delivery service with same-day shipping available in major metropolitan areas.
              </p>
            </div>

            <div className="group text-center">
              <div className="w-20 h-20 mx-auto mb-8 bg-gradient-to-br from-slate-100 to-slate-200 rounded-2xl flex items-center justify-center group-hover:from-amber-50 group-hover:to-amber-100 transition-all duration-300">
                <Sparkles className="h-10 w-10 text-slate-600 group-hover:text-amber-600 transition-colors duration-300" />
              </div>
              <h3 className="text-2xl font-light text-slate-900 mb-4">Bespoke Service</h3>
              <p className="text-slate-600 leading-relaxed">
                Personal styling consultations and custom alterations by our master craftspeople.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Latest Products Showcase */}
      <section className="py-24 bg-slate-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="inline-flex items-center space-x-2 bg-white rounded-full px-6 py-2 mb-6 shadow-sm">
              <Sparkles className="h-4 w-4 text-slate-600" />
              <span className="text-slate-600 text-sm font-medium uppercase tracking-wider">New Arrivals</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-light text-slate-900 mb-6">
              Latest
              <span className="font-normal"> Luxury</span>
            </h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">
              Discover our newest additions to the collection
            </p>
          </div>

          {/* Products Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            {products.slice(0, 3).map((product, index) => (
              <div
                key={product.id}
                className="group bg-white rounded-3xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                {/* Product Image */}
                <div className="relative overflow-hidden h-80">
                  <img
                    src={product.images?.[0] || '/placeholder-image.jpg'}
                    alt={product.name}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                  />

                  {/* Badges */}
                  <div className="absolute top-4 left-4 flex flex-col space-y-2">
                    {product.is_new && (
                      <span className="bg-gradient-to-r from-green-500 to-green-600 text-white px-3 py-1 rounded-full text-xs font-semibold shadow-lg">
                        NEW
                      </span>
                    )}
                    {product.is_sale && (
                      <span className="bg-gradient-to-r from-red-500 to-red-600 text-white px-3 py-1 rounded-full text-xs font-semibold shadow-lg">
                        SALE
                      </span>
                    )}
                  </div>

                  {/* Quick View Overlay */}
                  <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                    <Link
                      to={`/products/${product.id}`}
                      className="bg-white text-slate-900 px-6 py-3 rounded-full font-medium hover:bg-slate-100 transition-colors duration-300 transform translate-y-4 group-hover:translate-y-0"
                    >
                      Quick View
                    </Link>
                  </div>
                </div>

                {/* Product Info */}
                <div className="p-6">
                  {/* Brand */}
                  {product.brand && (
                    <p className="text-sm text-slate-500 uppercase tracking-wider mb-2">{product.brand}</p>
                  )}

                  {/* Product Name */}
                  <Link to={`/products/${product.id}`}>
                    <h3 className="text-xl font-light text-slate-900 hover:text-amber-600 transition-colors duration-300 mb-3 line-clamp-2">
                      {product.name}
                    </h3>
                  </Link>

                  {/* Rating */}
                  <div className="flex items-center space-x-2 mb-4">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`h-4 w-4 ${
                            i < Math.floor(product.rating)
                              ? 'text-amber-400 fill-current'
                              : 'text-slate-300'
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-sm text-slate-500">({product.reviews_count})</span>
                  </div>

                  {/* Price */}
                  <div className="flex items-center space-x-3 mb-6">
                    <span className="text-2xl font-light text-slate-900">${product.price}</span>
                    {product.original_price && (
                      <span className="text-lg text-slate-500 line-through">
                        ${product.original_price}
                      </span>
                    )}
                  </div>

                  {/* Action Button */}
                  <Link
                    to={`/products/${product.id}`}
                    className="w-full bg-slate-900 hover:bg-slate-800 text-white py-3 px-6 rounded-full font-medium transition-all duration-300 transform hover:scale-105 flex items-center justify-center space-x-2 group"
                  >
                    <span>View Details</span>
                    <ArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Link>
                </div>
              </div>
            ))}
          </div>

          {/* See All Products Button */}
          <div className="text-center">
            <Link
              to="/products"
              className="inline-flex items-center space-x-3 bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white px-12 py-4 rounded-full text-lg font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-2xl hover:shadow-amber-500/25"
            >
              <span>See All Products</span>
              <ArrowRight className="h-5 w-5" />
            </Link>
          </div>
        </div>
      </section>

      {/* Premium Brand Story */}
      <section className="py-32 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M50 0L60.876 39.124L100 50L60.876 60.876L50 100L39.124 60.876L0 50L39.124 39.124z' fill='%23ffffff' fill-opacity='0.1'/%3E%3C/svg%3E")`,
            backgroundRepeat: 'repeat'
          }} />
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <div className="inline-flex items-center space-x-2 bg-white/10 rounded-full px-6 py-2 mb-8 backdrop-blur-sm">
                <Sparkles className="h-4 w-4 text-amber-400" />
                <span className="text-amber-300 text-sm font-medium uppercase tracking-wider">Our Heritage</span>
              </div>

              <h2 className="text-5xl md:text-6xl font-light mb-8 leading-tight">
                Defining
                <span className="block font-normal bg-gradient-to-r from-amber-300 to-amber-500 bg-clip-text text-transparent">
                  Luxury
                </span>
                <span className="block font-light">Since 2020</span>
              </h2>

              <p className="text-xl text-slate-300 mb-8 leading-relaxed">
                Born from a vision to revolutionize luxury fashion, LUXE represents the perfect synthesis of
                <span className="text-white font-medium"> traditional craftsmanship</span> and
                <span className="text-white font-medium"> contemporary innovation</span>.
              </p>

              <p className="text-lg text-slate-400 mb-12 leading-relaxed">
                Each piece in our collection is a testament to our unwavering commitment to excellence,
                sustainability, and the artistry that transforms premium materials into timeless treasures.
              </p>

              {/* Achievement Stats */}
              <div className="grid grid-cols-3 gap-8 mb-12">
                <div className="text-center">
                  <div className="text-4xl font-light text-white mb-2">15+</div>
                  <div className="text-slate-400 text-sm uppercase tracking-wider">Years Mastery</div>
                </div>
                <div className="text-center border-x border-slate-700">
                  <div className="text-4xl font-light text-white mb-2">200+</div>
                  <div className="text-slate-400 text-sm uppercase tracking-wider">Master Artisans</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-light text-white mb-2">50+</div>
                  <div className="text-slate-400 text-sm uppercase tracking-wider">Countries Served</div>
                </div>
              </div>

              <Link
                to="/products"
                className="inline-flex items-center space-x-3 bg-gradient-to-r from-amber-500 to-amber-600 text-white px-8 py-4 rounded-full font-medium hover:from-amber-600 hover:to-amber-700 transition-all duration-300 transform hover:scale-105"
              >
                <span>Discover Our Story</span>
                <ArrowRight className="h-5 w-5" />
              </Link>
            </div>

            {/* Visual Elements */}
            <div className="relative">
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-6">
                  <div className="relative overflow-hidden rounded-2xl">
                    <img
                      src="https://images.pexels.com/photos/1926769/pexels-photo-1926769.jpeg?auto=compress&cs=tinysrgb&w=500"
                      alt="Master Craftsmanship"
                      className="w-full h-64 object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
                    <div className="absolute bottom-4 left-4 text-white">
                      <div className="text-sm font-medium">Master Craftsmanship</div>
                    </div>
                  </div>
                  <div className="relative overflow-hidden rounded-2xl">
                    <img
                      src="https://images.pexels.com/photos/1884581/pexels-photo-1884581.jpeg?auto=compress&cs=tinysrgb&w=500"
                      alt="Premium Materials"
                      className="w-full h-48 object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
                    <div className="absolute bottom-4 left-4 text-white">
                      <div className="text-sm font-medium">Premium Materials</div>
                    </div>
                  </div>
                </div>
                <div className="space-y-6 mt-12">
                  <div className="relative overflow-hidden rounded-2xl">
                    <img
                      src="https://images.pexels.com/photos/1536619/pexels-photo-1536619.jpeg?auto=compress&cs=tinysrgb&w=500"
                      alt="Timeless Design"
                      className="w-full h-48 object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
                    <div className="absolute bottom-4 left-4 text-white">
                      <div className="text-sm font-medium">Timeless Design</div>
                    </div>
                  </div>
                  <div className="relative overflow-hidden rounded-2xl">
                    <img
                      src="https://images.pexels.com/photos/1884581/pexels-photo-1884581.jpeg?auto=compress&cs=tinysrgb&w=500"
                      alt="Global Excellence"
                      className="w-full h-64 object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
                    <div className="absolute bottom-4 left-4 text-white">
                      <div className="text-sm font-medium">Global Excellence</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Floating Elements */}
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-amber-400/20 to-amber-600/20 rounded-full blur-xl" />
              <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-gradient-to-br from-slate-600/20 to-slate-800/20 rounded-full blur-xl" />
            </div>
          </div>
        </div>
      </section>

      {/* Premium Testimonials */}
      <section className="py-32 bg-white relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center mb-20">
            <div className="inline-flex items-center space-x-2 bg-slate-50 rounded-full px-6 py-2 mb-6">
              <Star className="h-4 w-4 text-slate-600" />
              <span className="text-slate-600 text-sm font-medium uppercase tracking-wider">Client Stories</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-light text-slate-900 mb-6">
              Trusted by
              <span className="font-normal"> Connoisseurs</span>
            </h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">
              Discover why discerning clients choose LUXE for their most important moments
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={testimonial.id} className={`group relative ${index === 1 ? 'lg:scale-105 lg:z-10' : ''}`}>
                <div className="bg-gradient-to-br from-slate-50 to-white p-8 rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 border border-slate-100 group-hover:border-amber-200">
                  {/* Quote Icon */}
                  <div className="w-12 h-12 bg-gradient-to-br from-amber-100 to-amber-200 rounded-2xl flex items-center justify-center mb-6">
                    <svg className="w-6 h-6 text-amber-600" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z"/>
                    </svg>
                  </div>

                  {/* Rating */}
                  <div className="flex items-center mb-6">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-5 w-5 text-amber-400 fill-current" />
                    ))}
                  </div>

                  {/* Testimonial Text */}
                  <p className="text-slate-700 text-lg leading-relaxed mb-8 font-light">
                    "{testimonial.text}"
                  </p>

                  {/* Customer Info */}
                  <div className="flex items-center">
                    <div className="w-14 h-14 rounded-full overflow-hidden mr-4 ring-2 ring-slate-100">
                      <img
                        src={testimonial.image}
                        alt={testimonial.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div>
                      <div className="font-medium text-slate-900 text-lg">{testimonial.name}</div>
                      <div className="text-slate-500 text-sm">Verified Customer</div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Background Elements */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-amber-100/30 to-amber-200/30 rounded-full blur-3xl" />
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-br from-slate-100/50 to-slate-200/50 rounded-full blur-3xl" />
      </section>

      {/* Premium Newsletter CTA */}
      <section className="py-24 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='1'%3E%3Cpath d='M30 30c0-11.046 8.954-20 20-20s20 8.954 20 20-8.954 20-20 20-20-8.954-20-20zm0 0c0 11.046-8.954 20-20 20s-20-8.954-20-20 8.954-20 20-20 20 8.954 20 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            backgroundRepeat: 'repeat'
          }} />
        </div>

        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="inline-flex items-center space-x-2 bg-white/10 rounded-full px-6 py-2 mb-8 backdrop-blur-sm">
            <Sparkles className="h-4 w-4 text-amber-400" />
            <span className="text-amber-300 text-sm font-medium uppercase tracking-wider">Exclusive Access</span>
          </div>

          <h2 className="text-4xl md:text-5xl font-light mb-6 leading-tight">
            Join the
            <span className="block font-normal bg-gradient-to-r from-amber-300 to-amber-500 bg-clip-text text-transparent">
              Inner Circle
            </span>
          </h2>

          <p className="text-xl text-slate-300 mb-12 max-w-2xl mx-auto leading-relaxed">
            Be the first to discover new collections, receive styling insights from our experts,
            and enjoy exclusive member benefits.
          </p>

          {subscribed ? (
            <div className="inline-flex items-center space-x-3 bg-gradient-to-r from-green-500 to-green-600 text-white px-8 py-4 rounded-full">
              <Check className="h-6 w-6" />
              <span className="font-medium">Welcome to the LUXE family! Check your email for exclusive offers.</span>
            </div>
          ) : (
            <form onSubmit={handleNewsletterSubmit} className="max-w-md mx-auto mb-8">
              <div className="flex flex-col sm:flex-row gap-4">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email address"
                  required
                  className="flex-1 px-6 py-4 bg-white/10 border border-white/20 rounded-full text-white placeholder-slate-300 focus:ring-2 focus:ring-amber-500 focus:border-transparent backdrop-blur-sm"
                />
                <button
                  type="submit"
                  className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 px-8 py-4 rounded-full font-medium transition-all duration-300 transform hover:scale-105 whitespace-nowrap"
                >
                  Join Now
                </button>
              </div>
            </form>
          )}

          <p className="text-slate-400 text-sm">
            Join 50,000+ discerning customers • Unsubscribe anytime • Privacy guaranteed
          </p>
        </div>

        {/* Floating Elements */}
        <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-amber-400 rounded-full animate-pulse opacity-60" />
        <div className="absolute top-3/4 right-1/3 w-1 h-1 bg-white rounded-full animate-pulse opacity-40" />
        <div className="absolute top-1/2 right-1/4 w-1.5 h-1.5 bg-amber-300 rounded-full animate-pulse opacity-50" />
      </section>
    </div>
  );
};

export default Home;