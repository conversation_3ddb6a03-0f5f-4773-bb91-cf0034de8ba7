import { supabase } from '../lib/supabase';

export const testDatabaseTriggers = async () => {
  console.log('🔍 Testing Database Triggers and Functions...');
  
  try {
    // Test 1: Check if profiles table structure is correct
    console.log('1. Testing profiles table structure...');
    
    const { data: profilesData, error: profilesError } = await supabase
      .from('profiles')
      .select('*')
      .limit(1);
    
    if (profilesError) {
      console.error('❌ Profiles table error:', profilesError);
      return false;
    } else {
      console.log('✅ Profiles table accessible');
    }
    
    // Test 2: Check if we can manually insert into profiles table
    console.log('2. Testing manual profile insertion...');
    
    const testProfileId = 'test-' + Date.now();
    const { data: insertData, error: insertError } = await supabase
      .from('profiles')
      .insert({
        id: testProfileId,
        email: '<EMAIL>',
        full_name: 'Test User',
        role: 'user'
      })
      .select();
    
    if (insertError) {
      console.error('❌ Manual profile insertion failed:', insertError);
      
      // Check if it's a foreign key constraint issue
      if (insertError.message.includes('foreign key') || insertError.message.includes('violates')) {
        console.error('🔍 This suggests the profiles table references auth.users incorrectly');
      }
    } else {
      console.log('✅ Manual profile insertion successful');
      
      // Clean up test data
      await supabase.from('profiles').delete().eq('id', testProfileId);
      console.log('🧹 Test data cleaned up');
    }
    
    // Test 3: Check auth.users table accessibility (this might fail due to RLS)
    console.log('3. Testing auth.users table access...');
    
    try {
      const { data: authData, error: authError } = await supabase
        .from('auth.users')
        .select('id, email')
        .limit(1);
      
      if (authError) {
        console.log('⚠️ Cannot access auth.users directly (expected):', authError.message);
      } else {
        console.log('✅ Auth.users accessible:', authData?.length || 0, 'users');
      }
    } catch (err) {
      console.log('⚠️ Auth.users access failed (expected for security)');
    }
    
    // Test 4: Check if handle_new_user function exists
    console.log('4. Testing handle_new_user function...');
    
    try {
      // Try to call the function (this will fail but tells us if it exists)
      const { error: functionError } = await supabase.rpc('handle_new_user');
      
      if (functionError) {
        if (functionError.message.includes('function handle_new_user() does not exist')) {
          console.error('❌ handle_new_user function does not exist');
        } else {
          console.log('✅ handle_new_user function exists (got expected error):', functionError.message);
        }
      }
    } catch (err) {
      console.log('⚠️ Function test completed');
    }
    
    // Test 5: Check database permissions
    console.log('5. Testing database permissions...');
    
    const { data: permissionsData, error: permissionsError } = await supabase
      .from('profiles')
      .select('count', { count: 'exact', head: true });
    
    if (permissionsError) {
      console.error('❌ Permissions issue:', permissionsError);
    } else {
      console.log('✅ Database permissions appear correct');
    }
    
    console.log('🎉 Database diagnostic completed!');
    return true;
    
  } catch (error) {
    console.error('💥 Unexpected error during database diagnostic:', error);
    return false;
  }
};

export const checkSupabaseProjectSettings = () => {
  console.log('🔧 Checking Supabase Project Settings...');
  
  const url = import.meta.env.VITE_SUPABASE_URL;
  
  if (url) {
    console.log('🌐 Project URL:', url);
    
    // Extract project reference from URL
    const match = url.match(/https:\/\/([^.]+)\.supabase\.co/);
    if (match) {
      const projectRef = match[1];
      console.log('📋 Project Reference:', projectRef);
      console.log('🔗 Dashboard URL:', `https://supabase.com/dashboard/project/${projectRef}`);
      console.log('🔗 Auth Settings:', `https://supabase.com/dashboard/project/${projectRef}/auth/users`);
      console.log('🔗 Database:', `https://supabase.com/dashboard/project/${projectRef}/editor`);
    }
  }
  
  console.log('💡 Things to check in Supabase Dashboard:');
  console.log('  1. Auth > Settings > Enable email confirmations');
  console.log('  2. Database > Functions > Check if handle_new_user exists');
  console.log('  3. Database > Triggers > Check if on_auth_user_created exists');
  console.log('  4. Auth > Policies > Check RLS policies on profiles table');
};
