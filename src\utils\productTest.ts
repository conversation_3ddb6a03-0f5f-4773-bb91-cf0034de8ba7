import { supabase } from '../lib/supabase';

export const testProductFetching = async () => {
  console.log('🧪 Testing Product Fetching Performance...');
  
  try {
    // Test 1: Fetch all products (like the home page does)
    console.log('1. Testing bulk product fetch...');
    const startTime1 = performance.now();
    
    const { data: allProducts, error: allError } = await supabase
      .from('products')
      .select(`
        *,
        categories (
          name,
          slug
        )
      `)
      .order('created_at', { ascending: false });
    
    const endTime1 = performance.now();
    
    if (allError) {
      console.error('❌ Bulk fetch failed:', allError);
    } else {
      console.log(`✅ Bulk fetch successful: ${allProducts?.length || 0} products in ${(endTime1 - startTime1).toFixed(2)}ms`);
    }
    
    // Test 2: Fetch single product by ID (like ProductDetail does)
    if (allProducts && allProducts.length > 0) {
      const testProductId = allProducts[0].id;
      console.log(`2. Testing single product fetch for ID: ${testProductId}`);
      
      const startTime2 = performance.now();
      
      const { data: singleProduct, error: singleError } = await supabase
        .from('products')
        .select(`
          *,
          categories (
            name,
            slug
          )
        `)
        .eq('id', testProductId)
        .single();
      
      const endTime2 = performance.now();
      
      if (singleError) {
        console.error('❌ Single fetch failed:', singleError);
      } else {
        console.log(`✅ Single fetch successful in ${(endTime2 - startTime2).toFixed(2)}ms`);
        console.log('📦 Product data structure:', {
          id: singleProduct.id,
          name: singleProduct.name,
          hasImages: !!singleProduct.images && singleProduct.images.length > 0,
          hasSizes: !!singleProduct.sizes && singleProduct.sizes.length > 0,
          hasColors: !!singleProduct.colors && singleProduct.colors.length > 0,
          hasCareInstructions: !!singleProduct.care_instructions && singleProduct.care_instructions.length > 0,
          hasCategory: !!singleProduct.categories
        });
      }
      
      // Test 3: Multiple rapid fetches (simulate navigation)
      console.log('3. Testing rapid sequential fetches...');
      const rapidStartTime = performance.now();
      
      const rapidPromises = allProducts.slice(0, 3).map(product => 
        supabase
          .from('products')
          .select(`
            *,
            categories (
              name,
              slug
            )
          `)
          .eq('id', product.id)
          .single()
      );
      
      const rapidResults = await Promise.all(rapidPromises);
      const rapidEndTime = performance.now();
      
      const successCount = rapidResults.filter(result => !result.error).length;
      console.log(`✅ Rapid fetches: ${successCount}/3 successful in ${(rapidEndTime - rapidStartTime).toFixed(2)}ms`);
      
      // Test 4: Check for potential data issues
      console.log('4. Checking data integrity...');
      
      const dataIssues = [];
      
      allProducts.forEach((product, index) => {
        if (!product.images || product.images.length === 0) {
          dataIssues.push(`Product ${index + 1} (${product.name}) has no images`);
        }
        if (!product.sizes || product.sizes.length === 0) {
          dataIssues.push(`Product ${index + 1} (${product.name}) has no sizes`);
        }
        if (!product.colors || product.colors.length === 0) {
          dataIssues.push(`Product ${index + 1} (${product.name}) has no colors`);
        }
        if (!product.care_instructions || product.care_instructions.length === 0) {
          dataIssues.push(`Product ${index + 1} (${product.name}) has no care instructions`);
        }
      });
      
      if (dataIssues.length > 0) {
        console.warn('⚠️ Data integrity issues found:');
        dataIssues.forEach(issue => console.warn(`  - ${issue}`));
      } else {
        console.log('✅ All products have complete data');
      }
    }
    
    console.log('🎉 Product fetching test completed!');
    
  } catch (error) {
    console.error('💥 Unexpected error during product test:', error);
  }
};
