import { supabase } from '../lib/supabase';

export const testSupabaseConnection = async () => {
  console.log('🔍 Testing Supabase Connection...');
  
  try {
    // Test 1: Basic connection
    console.log('1. Testing basic connection...');
    const { data, error } = await supabase.from('products').select('count', { count: 'exact' });
    
    if (error) {
      console.error('❌ Connection failed:', error);
      return false;
    }
    
    console.log('✅ Basic connection successful');
    console.log(`📊 Products count: ${data?.length || 0}`);
    
    // Test 2: Check if tables exist
    console.log('2. Testing table structure...');
    const tables = ['products', 'categories', 'profiles', 'cart_items', 'orders', 'order_items'];
    
    for (const table of tables) {
      try {
        const { error: tableError } = await supabase.from(table).select('*').limit(1);
        if (tableError) {
          console.error(`❌ Table '${table}' error:`, tableError);
        } else {
          console.log(`✅ Table '${table}' accessible`);
        }
      } catch (err) {
        console.error(`❌ Table '${table}' failed:`, err);
      }
    }
    
    // Test 3: Check products data
    console.log('3. Testing products data...');
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select('*')
      .limit(5);
    
    if (productsError) {
      console.error('❌ Products query failed:', productsError);
    } else {
      console.log(`✅ Products query successful. Found ${products?.length || 0} products`);
      if (products && products.length > 0) {
        console.log('📦 Sample product:', {
          id: products[0].id,
          name: products[0].name,
          price: products[0].price,
          images: products[0].images?.length || 0,
          sizes: products[0].sizes?.length || 0,
          colors: products[0].colors?.length || 0
        });
      }
    }
    
    // Test 4: Check categories data
    console.log('4. Testing categories data...');
    const { data: categories, error: categoriesError } = await supabase
      .from('categories')
      .select('*');
    
    if (categoriesError) {
      console.error('❌ Categories query failed:', categoriesError);
    } else {
      console.log(`✅ Categories query successful. Found ${categories?.length || 0} categories`);
      if (categories && categories.length > 0) {
        console.log('📂 Categories:', categories.map(c => c.name).join(', '));
      }
    }
    
    // Test 5: Test product with category join
    console.log('5. Testing product-category relationship...');
    const { data: productsWithCategories, error: joinError } = await supabase
      .from('products')
      .select(`
        id,
        name,
        price,
        categories (
          name,
          slug
        )
      `)
      .limit(3);
    
    if (joinError) {
      console.error('❌ Product-category join failed:', joinError);
    } else {
      console.log(`✅ Product-category join successful. Found ${productsWithCategories?.length || 0} products with categories`);
      if (productsWithCategories && productsWithCategories.length > 0) {
        console.log('🔗 Sample product with category:', {
          name: productsWithCategories[0].name,
          category: productsWithCategories[0].categories?.name || 'No category'
        });
      }
    }
    
    // Test 6: Check authentication status
    console.log('6. Testing authentication...');
    const { data: { session }, error: authError } = await supabase.auth.getSession();
    
    if (authError) {
      console.error('❌ Auth check failed:', authError);
    } else {
      console.log('✅ Auth check successful');
      console.log(`👤 Current user: ${session?.user?.email || 'Not logged in'}`);
    }
    
    // Test 7: Check RLS policies (this might fail if not authenticated)
    console.log('7. Testing Row Level Security...');
    const { data: profilesData, error: rlsError } = await supabase
      .from('profiles')
      .select('*')
      .limit(1);
    
    if (rlsError) {
      console.log('⚠️ RLS blocking access (expected if not authenticated):', rlsError.message);
    } else {
      console.log(`✅ Profiles accessible. Found ${profilesData?.length || 0} profiles`);
    }
    
    // Test 8: Test registration flow (without actually creating a user)
    console.log('8. Testing registration configuration...');
    try {
      // Test if we can access the auth.users table structure (this will fail but shows if auth is configured)
      const { error: authError } = await supabase.auth.getSession();
      if (authError) {
        console.error('❌ Auth configuration issue:', authError.message);
      } else {
        console.log('✅ Auth configuration appears correct');
      }

      // Check if the handle_new_user function exists by trying to call it (this will fail but shows if trigger exists)
      const { error: functionError } = await supabase.rpc('handle_new_user');
      if (functionError && functionError.message.includes('function handle_new_user() does not exist')) {
        console.error('❌ User creation trigger not found');
      } else {
        console.log('✅ User creation trigger appears to be configured');
      }
    } catch (err) {
      console.log('⚠️ Auth configuration test completed with expected errors');
    }

    console.log('🎉 Supabase connection test completed!');
    return true;
    
  } catch (error) {
    console.error('💥 Unexpected error during Supabase test:', error);
    return false;
  }
};

export const checkEnvironmentVariables = () => {
  console.log('🔧 Checking Environment Variables...');
  
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
  
  console.log('VITE_SUPABASE_URL:', supabaseUrl ? '✅ Set' : '❌ Missing');
  console.log('VITE_SUPABASE_ANON_KEY:', supabaseAnonKey ? '✅ Set' : '❌ Missing');
  
  if (supabaseUrl) {
    console.log('🌐 Supabase URL:', supabaseUrl);
  }
  
  if (supabaseAnonKey) {
    console.log('🔑 Anon Key (first 20 chars):', supabaseAnonKey.substring(0, 20) + '...');
  }
  
  return !!(supabaseUrl && supabaseAnonKey);
};
