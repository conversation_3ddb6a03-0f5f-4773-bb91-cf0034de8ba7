// Temporary database access functions that bypass RLS issues
// Use these until RLS policies are fixed

import { supabase } from '../lib/supabase';

// Temporary product data - use this if database fails
const FALLBACK_PRODUCTS = [
  {
    id: '1',
    name: 'Classic Wool Blazer',
    description: 'A timeless wool blazer perfect for professional settings.',
    price: 299,
    original_price: null,
    images: ['/api/placeholder/400/600', '/api/placeholder/400/600'],
    sizes: ['XS', 'S', 'M', 'L', 'XL'],
    colors: ['Black', 'Navy', 'Gray'],
    brand: 'LUXE',
    material: 'Wool',
    care_instructions: ['Dry clean only', 'Do not bleach'],
    stock_quantity: 15,
    category_id: '1',
    is_new: false,
    is_sale: false,
    rating: 4.5,
    reviews_count: 23,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    categories: { name: 'Blazers', slug: 'blazers' }
  },
  {
    id: '2',
    name: 'Elegant Evening Dress',
    description: 'A sophisticated evening dress for special occasions.',
    price: 199,
    original_price: 249,
    images: ['/api/placeholder/400/600', '/api/placeholder/400/600'],
    sizes: ['XS', 'S', 'M', 'L'],
    colors: ['Black', 'Red', 'Blue'],
    brand: 'LUXE',
    material: 'Silk',
    care_instructions: ['Hand wash only', 'Hang to dry'],
    stock_quantity: 8,
    category_id: '2',
    is_new: true,
    is_sale: true,
    rating: 4.8,
    reviews_count: 15,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    categories: { name: 'Dresses', slug: 'dresses' }
  },
  {
    id: '3',
    name: 'Premium Leather Shoes',
    description: 'Handcrafted leather shoes with exceptional comfort.',
    price: 159,
    original_price: null,
    images: ['/api/placeholder/400/600', '/api/placeholder/400/600'],
    sizes: ['36', '37', '38', '39', '40', '41', '42'],
    colors: ['Black', 'Brown', 'Tan'],
    brand: 'LUXE',
    material: 'Leather',
    care_instructions: ['Clean with leather cleaner', 'Store in dust bag'],
    stock_quantity: 12,
    category_id: '3',
    is_new: false,
    is_sale: false,
    rating: 4.6,
    reviews_count: 31,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    categories: { name: 'Shoes', slug: 'shoes' }
  }
];

const FALLBACK_CATEGORIES = [
  { id: '1', name: 'Blazers', slug: 'blazers', description: 'Professional blazers' },
  { id: '2', name: 'Dresses', slug: 'dresses', description: 'Elegant dresses' },
  { id: '3', name: 'Shoes', slug: 'shoes', description: 'Premium footwear' },
  { id: '4', name: 'Sweaters', slug: 'sweaters', description: 'Cozy sweaters' },
  { id: '5', name: 'Pants', slug: 'pants', description: 'Stylish pants' },
  { id: '6', name: 'Accessories', slug: 'accessories', description: 'Fashion accessories' }
];

export const getProductsSafely = async () => {
  try {
    console.log('🛍️ Attempting to fetch products from database...');
    const startTime = performance.now();

    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        categories (
          name,
          slug
        )
      `)
      .order('created_at', { ascending: false });

    const endTime = performance.now();
    const fetchTime = (endTime - startTime).toFixed(2);

    if (error) {
      console.error('❌ Database products fetch failed:', {
        error: error,
        code: error.code,
        message: error.message,
        fetchTime: `${fetchTime}ms`
      });

      if (error.code === '42P17') {
        console.error('🔄 INFINITE RECURSION ERROR - This should be fixed now!');
      }

      console.log('🔄 Using fallback products data');
      return FALLBACK_PRODUCTS;
    }

    console.log('✅ Products fetched from database successfully:', {
      count: data?.length || 0,
      fetchTime: `${fetchTime}ms`,
      hasCategories: data?.some(p => p.categories) || false
    });
    return data || FALLBACK_PRODUCTS;
  } catch (err) {
    console.error('💥 Unexpected error fetching products:', err);
    console.log('🔄 Using fallback products data');
    return FALLBACK_PRODUCTS;
  }
};

export const getProductByIdSafely = async (id: string) => {
  try {
    console.log('🛍️ Attempting to fetch product by ID from database...');
    
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        categories (
          name,
          slug
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error('❌ Database product fetch failed:', error);
      console.log('🔄 Using fallback product data');
      return FALLBACK_PRODUCTS.find(p => p.id === id) || FALLBACK_PRODUCTS[0];
    }

    console.log('✅ Product fetched from database successfully');
    return data;
  } catch (err) {
    console.error('💥 Unexpected error fetching product:', err);
    console.log('🔄 Using fallback product data');
    return FALLBACK_PRODUCTS.find(p => p.id === id) || FALLBACK_PRODUCTS[0];
  }
};

export const getCategoriesSafely = async () => {
  try {
    console.log('📂 Attempting to fetch categories from database...');
    
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .order('name');

    if (error) {
      console.error('❌ Database categories fetch failed:', error);
      console.log('🔄 Using fallback categories data');
      return FALLBACK_CATEGORIES;
    }

    console.log('✅ Categories fetched from database successfully');
    return data || FALLBACK_CATEGORIES;
  } catch (err) {
    console.error('💥 Unexpected error fetching categories:', err);
    console.log('🔄 Using fallback categories data');
    return FALLBACK_CATEGORIES;
  }
};
