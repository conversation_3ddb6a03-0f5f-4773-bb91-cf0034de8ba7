// Simple Node.js script to test Supabase connection
// Run with: node supabase-check.js

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://xjdlvfqlgzrryumtssmo.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhqZGx2ZnFsZ3pycnl1bXRzc21vIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI5NTE1NDIsImV4cCI6MjA2ODUyNzU0Mn0.vkN3cg76CIKDARKd0ur_4u8_mRNjO3HwUgZwWA0ad9I';

console.log('🔍 Supabase Connection Diagnostic');
console.log('================================');
console.log('URL:', supabaseUrl);
console.log('Key (first 20 chars):', supabaseKey.substring(0, 20) + '...');
console.log('');

const supabase = createClient(supabaseUrl, supabaseKey);

async function runDiagnostics() {
  try {
    console.log('1. Testing basic connection...');
    const { data, error } = await supabase.from('products').select('count', { count: 'exact' });
    
    if (error) {
      console.error('❌ Connection failed:', error.message);
      console.error('Full error:', error);
      return;
    }
    
    console.log('✅ Connection successful');
    
    console.log('\n2. Testing products table...');
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select('*')
      .limit(3);
    
    if (productsError) {
      console.error('❌ Products query failed:', productsError.message);
    } else {
      console.log(`✅ Found ${products.length} products`);
      if (products.length > 0) {
        console.log('Sample product:', {
          name: products[0].name,
          price: products[0].price,
          id: products[0].id
        });
      }
    }
    
    console.log('\n3. Testing categories table...');
    const { data: categories, error: categoriesError } = await supabase
      .from('categories')
      .select('*');
    
    if (categoriesError) {
      console.error('❌ Categories query failed:', categoriesError.message);
    } else {
      console.log(`✅ Found ${categories.length} categories`);
      console.log('Categories:', categories.map(c => c.name).join(', '));
    }
    
    console.log('\n4. Testing product-category join...');
    const { data: joined, error: joinError } = await supabase
      .from('products')
      .select(`
        name,
        price,
        categories (
          name,
          slug
        )
      `)
      .limit(2);
    
    if (joinError) {
      console.error('❌ Join query failed:', joinError.message);
    } else {
      console.log(`✅ Join successful, ${joined.length} products with categories`);
      if (joined.length > 0) {
        console.log('Sample:', {
          product: joined[0].name,
          category: joined[0].categories?.name || 'No category'
        });
      }
    }
    
    console.log('\n🎉 Diagnostics completed!');
    
  } catch (error) {
    console.error('💥 Unexpected error:', error);
  }
}

runDiagnostics();
