/*
  # Add Blog Posts Table

  1. New Table
    - `blog_posts`
      - `id` (uuid, primary key)
      - `title` (text)
      - `slug` (text, unique)
      - `excerpt` (text)
      - `content` (text)
      - `featured_image` (text)
      - `author_id` (uuid, references profiles)
      - `status` (text, default 'draft')
      - `tags` (text array)
      - `meta_title` (text)
      - `meta_description` (text)
      - `published_at` (timestamp, nullable)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)

  2. Security
    - Enable RLS on blog_posts table
    - Add policies for public read access to published posts
    - Add policies for admin users to manage all posts
*/

-- Create blog post status enum
CREATE TYPE blog_post_status AS ENUM ('draft', 'published', 'archived');

-- Create blog_posts table
CREATE TABLE IF NOT EXISTS blog_posts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  slug text UNIQUE NOT NULL,
  excerpt text,
  content text NOT NULL,
  featured_image text,
  author_id uuid REFERENCES profiles(id) ON DELETE SET NULL,
  status blog_post_status DEFAULT 'draft',
  tags text[] DEFAULT '{}',
  meta_title text,
  meta_description text,
  published_at timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create indexes for better performance
CREATE INDEX idx_blog_posts_slug ON blog_posts(slug);
CREATE INDEX idx_blog_posts_status ON blog_posts(status);
CREATE INDEX idx_blog_posts_published_at ON blog_posts(published_at);
CREATE INDEX idx_blog_posts_author_id ON blog_posts(author_id);

-- Enable Row Level Security
ALTER TABLE blog_posts ENABLE ROW LEVEL SECURITY;

-- Blog posts policies
-- Public can read published posts
CREATE POLICY "Anyone can read published blog posts"
  ON blog_posts
  FOR SELECT
  TO anon, authenticated
  USING (status = 'published' AND published_at IS NOT NULL);

-- Authenticated users can read published posts
CREATE POLICY "Authenticated users can read published blog posts"
  ON blog_posts
  FOR SELECT
  TO authenticated
  USING (status = 'published' AND published_at IS NOT NULL);

-- Admins can read all posts
CREATE POLICY "Admins can read all blog posts"
  ON blog_posts
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Admins can insert posts
CREATE POLICY "Admins can insert blog posts"
  ON blog_posts
  FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Admins can update posts
CREATE POLICY "Admins can update blog posts"
  ON blog_posts
  FOR UPDATE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Admins can delete posts
CREATE POLICY "Admins can delete blog posts"
  ON blog_posts
  FOR DELETE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_blog_posts_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_blog_posts_updated_at
  BEFORE UPDATE ON blog_posts
  FOR EACH ROW
  EXECUTE FUNCTION update_blog_posts_updated_at();

-- Insert some sample blog posts
INSERT INTO blog_posts (
  title, slug, excerpt, content, featured_image, author_id, status, tags, 
  meta_title, meta_description, published_at
) VALUES
  (
    'The Art of Luxury Fashion: Craftsmanship Meets Innovation',
    'art-of-luxury-fashion-craftsmanship-innovation',
    'Discover how traditional craftsmanship and modern innovation come together to create timeless luxury pieces that define contemporary elegance.',
    'In the world of luxury fashion, the marriage between time-honored craftsmanship and cutting-edge innovation creates pieces that transcend mere clothing to become works of art. At LUXE, we believe that true luxury lies not just in the finest materials, but in the story each piece tells and the meticulous attention to detail that goes into its creation.

## The Heritage of Craftsmanship

Our master artisans bring decades of experience to every stitch, every seam, and every detail. These skilled craftspeople have honed their techniques through years of dedication, passing down knowledge from generation to generation. When you hold a LUXE piece, you''re not just holding a garment – you''re holding a piece of history, a testament to human skill and artistry.

## Innovation in Design

While we honor traditional techniques, we also embrace innovation. Our design team constantly explores new materials, sustainable practices, and cutting-edge construction methods. This fusion of old and new allows us to create pieces that are both timeless and thoroughly modern.

## The LUXE Difference

What sets LUXE apart is our unwavering commitment to excellence. Every piece undergoes rigorous quality control, ensuring that when it reaches you, it meets our exacting standards. From the initial sketch to the final stitch, every step is carefully monitored and executed with precision.

## Sustainability and Ethics

Modern luxury must also be responsible luxury. We''re committed to sustainable practices, ethical sourcing, and supporting the communities where our pieces are made. This commitment to responsibility is woven into every aspect of our business, from design to delivery.',
    'https://images.pexels.com/photos/1926769/pexels-photo-1926769.jpeg?auto=compress&cs=tinysrgb&w=1200',
    (SELECT id FROM profiles WHERE role = 'admin' LIMIT 1),
    'published',
    ARRAY['fashion', 'craftsmanship', 'luxury', 'innovation'],
    'The Art of Luxury Fashion | LUXE Blog',
    'Discover how traditional craftsmanship and modern innovation create timeless luxury fashion pieces at LUXE.',
    now() - interval '7 days'
  ),
  (
    'Sustainable Luxury: The Future of Fashion',
    'sustainable-luxury-future-of-fashion',
    'Explore how luxury fashion is evolving to embrace sustainability without compromising on quality, style, or the premium experience.',
    'The luxury fashion industry is undergoing a profound transformation. Today''s discerning consumers demand not only exceptional quality and style but also ethical practices and environmental responsibility. At LUXE, we''re proud to be at the forefront of this sustainable luxury movement.

## Redefining Luxury

Sustainability is no longer optional in luxury fashion – it''s essential. We''ve redefined what luxury means in the 21st century, proving that exceptional quality and environmental responsibility can coexist beautifully.

## Our Sustainable Practices

### Ethical Sourcing
We work exclusively with suppliers who share our commitment to fair labor practices and environmental stewardship. Every material in our collection is carefully sourced to ensure it meets our strict ethical standards.

### Quality Over Quantity
By creating pieces that are built to last, we encourage a more thoughtful approach to fashion consumption. Our garments are designed to be treasured for years, not seasons.

### Innovative Materials
We''re constantly exploring new sustainable materials that don''t compromise on luxury. From organic silks to recycled precious metals, innovation drives our material choices.

## The Impact

Our commitment to sustainability extends beyond our products to our entire operation. We''ve achieved carbon-neutral status and continue to find new ways to reduce our environmental footprint while maintaining the highest standards of luxury.',
    'https://images.pexels.com/photos/1884581/pexels-photo-1884581.jpeg?auto=compress&cs=tinysrgb&w=1200',
    (SELECT id FROM profiles WHERE role = 'admin' LIMIT 1),
    'published',
    ARRAY['sustainability', 'luxury', 'fashion', 'ethics', 'environment'],
    'Sustainable Luxury Fashion | LUXE Blog',
    'Learn how LUXE is leading the sustainable luxury fashion movement without compromising on quality or style.',
    now() - interval '3 days'
  ),
  (
    'Behind the Scenes: A Day in Our Atelier',
    'behind-scenes-day-in-our-atelier',
    'Take an exclusive look inside our design studio and discover the passion, precision, and artistry that goes into creating each LUXE piece.',
    'Step behind the curtain and discover the magic that happens in our atelier every day. From the first sketch to the final fitting, every LUXE piece is a labor of love, crafted by passionate artisans who take pride in their work.

## Morning: The Creative Process Begins

Our day starts early in the design studio, where our creative team reviews sketches, selects fabrics, and discusses the vision for new pieces. This is where inspiration meets reality, where dreams take shape.

## The Art of Pattern Making

Our master pattern makers are true artists, translating two-dimensional designs into three-dimensional reality. Using both traditional techniques and modern technology, they create the templates that will guide every cut and seam.

## Craftsmanship in Action

In our workshop, skilled artisans bring designs to life. Each piece is carefully constructed by hand, with attention to every detail. From hand-sewn buttonholes to perfectly aligned patterns, no detail is too small.

## Quality Control

Before any piece leaves our atelier, it undergoes rigorous quality control. Our standards are exacting, and only pieces that meet our criteria bear the LUXE name.

## The Final Touch

The last step is often the most important – the final press, the careful packaging, and the knowledge that another piece of wearable art is ready to find its home.',
    'https://images.pexels.com/photos/1536619/pexels-photo-1536619.jpeg?auto=compress&cs=tinysrgb&w=1200',
    (SELECT id FROM profiles WHERE role = 'admin' LIMIT 1),
    'draft',
    ARRAY['atelier', 'craftsmanship', 'behind-the-scenes', 'design'],
    'Behind the Scenes at LUXE Atelier | LUXE Blog',
    'Get an exclusive look inside the LUXE atelier and discover the artistry behind every piece.',
    NULL
  );
